/* Trust Plus Custom Styles */

/* Global Styles */
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #11998e;
    --danger-color: #ff6b6b;
    --warning-color: #feca57;
    --info-color: #54a0ff;
    --light-color: #f8f9fa;
    --dark-color: #2c3e50;
    --border-radius: 10px;
    --box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    --transition: all 0.3s ease;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--light-color);
    line-height: 1.6;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.fade-in {
    animation: fadeIn 0.6s ease-out;
}

.slide-in {
    animation: slideIn 0.5s ease-out;
}

/* Custom Components */
.btn {
    border-radius: var(--border-radius);
    font-weight: 600;
    padding: 10px 20px;
    transition: var(--transition);
    border: none;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #38ef7d 100%);
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color) 0%, #ee5a52 100%);
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #ff9ff3 100%);
}

.btn-info {
    background: linear-gradient(135deg, var(--info-color) 0%, #2ed573 100%);
}

/* Cards */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border: none;
    padding: 20px;
}

.card-title {
    margin-bottom: 0;
    font-weight: 600;
}

/* Forms */
.form-control, .form-select {
    border-radius: var(--border-radius);
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    transition: var(--transition);
    font-size: 14px;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 8px;
}

/* Tables */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.table thead th {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border: none;
    font-weight: 600;
    padding: 15px;
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.05);
}

.table td {
    padding: 12px 15px;
    vertical-align: middle;
}

/* Badges */
.badge {
    border-radius: 20px;
    padding: 8px 15px;
    font-weight: 500;
    font-size: 12px;
}

.badge-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #38ef7d 100%);
}

.badge-danger {
    background: linear-gradient(135deg, var(--danger-color) 0%, #ee5a52 100%);
}

.badge-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #ff9ff3 100%);
    color: var(--dark-color);
}

.badge-info {
    background: linear-gradient(135deg, var(--info-color) 0%, #2ed573 100%);
}

.badge-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.badge-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
}

/* Alerts */
.alert {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 15px 20px;
}

.alert-success {
    background: linear-gradient(135deg, rgba(17, 153, 142, 0.1) 0%, rgba(56, 239, 125, 0.1) 100%);
    border-left: 4px solid var(--success-color);
    color: var(--success-color);
}

.alert-danger {
    background: linear-gradient(135deg, rgba(255, 107, 107, 0.1) 0%, rgba(238, 90, 82, 0.1) 100%);
    border-left: 4px solid var(--danger-color);
    color: var(--danger-color);
}

.alert-warning {
    background: linear-gradient(135deg, rgba(254, 202, 87, 0.1) 0%, rgba(255, 159, 243, 0.1) 100%);
    border-left: 4px solid var(--warning-color);
    color: #856404;
}

.alert-info {
    background: linear-gradient(135deg, rgba(84, 160, 255, 0.1) 0%, rgba(46, 213, 115, 0.1) 100%);
    border-left: 4px solid var(--info-color);
    color: var(--info-color);
}

/* Pagination */
.pagination .page-link {
    border-radius: var(--border-radius);
    border: 2px solid #e9ecef;
    color: var(--primary-color);
    margin: 0 2px;
    transition: var(--transition);
}

.pagination .page-link:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.pagination .page-item.active .page-link {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border-color: var(--primary-color);
}

/* Loading Spinner */
.spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Utility Classes */
.text-primary {
    color: var(--primary-color) !important;
}

.bg-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
}

.border-primary {
    border-color: var(--primary-color) !important;
}

.shadow-sm {
    box-shadow: var(--box-shadow) !important;
}

.rounded-lg {
    border-radius: 15px !important;
}

.transition {
    transition: var(--transition) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        top: 0;
        right: -100%;
        width: 280px;
        height: 100vh;
        z-index: 1050;
        transition: var(--transition);
    }
    
    .sidebar.show {
        right: 0;
    }
    
    .main-content {
        margin-right: 0;
        border-radius: 0;
    }
    
    .stats-card {
        margin-bottom: 15px;
    }
    
    .hero-title {
        font-size: 2.5rem !important;
    }
    
    .feature-card {
        margin-bottom: 30px;
    }
}

/* Print Styles */
@media print {
    .sidebar,
    .navbar,
    .btn,
    .pagination {
        display: none !important;
    }
    
    .main-content {
        margin: 0 !important;
        box-shadow: none !important;
        border-radius: 0 !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
    
    .table {
        box-shadow: none !important;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --light-color: #1a1a1a;
        --dark-color: #ffffff;
    }
    
    body {
        background-color: var(--light-color);
        color: var(--dark-color);
    }
    
    .card {
        background-color: #2d2d2d;
        color: var(--dark-color);
    }
    
    .form-control, .form-select {
        background-color: #2d2d2d;
        border-color: #404040;
        color: var(--dark-color);
    }
    
    .table {
        background-color: #2d2d2d;
        color: var(--dark-color);
    }
}
