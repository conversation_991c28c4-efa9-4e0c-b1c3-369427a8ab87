<?php
/**
 * نموذج العملة
 * Currency Model
 */

require_once 'BaseModel.php';

class Currency extends BaseModel
{
    protected $table = 'currencies';
    protected $fillable = [
        'code', 'name', 'name_ar', 'symbol', 'decimal_places', 'is_active'
    ];
    
    /**
     * البحث عن عملة بالكود
     */
    public function findByCode($code)
    {
        return $this->findBy('code', $code);
    }
    
    /**
     * الحصول على العملات النشطة
     */
    public function getActiveCurrencies()
    {
        return $this->where(['is_active' => 1], 'name');
    }
    
    /**
     * الحصول على أسعار الصرف للعملة
     */
    public function getExchangeRates($currencyCode)
    {
        $sql = "SELECT er.*, 
                       fc.name as from_currency_name, fc.symbol as from_currency_symbol,
                       tc.name as to_currency_name, tc.symbol as to_currency_symbol
                FROM exchange_rates er
                JOIN currencies fc ON er.from_currency = fc.code
                JOIN currencies tc ON er.to_currency = tc.code
                WHERE (er.from_currency = ? OR er.to_currency = ?) 
                AND er.is_active = 1
                ORDER BY er.effective_date DESC";
        
        return $this->db->fetchAll($sql, [$currencyCode, $currencyCode]);
    }
    
    /**
     * الحصول على سعر الصرف بين عملتين
     */
    public function getExchangeRate($fromCurrency, $toCurrency, $date = null)
    {
        if ($fromCurrency === $toCurrency) {
            return 1.0;
        }
        
        $sql = "SELECT rate FROM exchange_rates 
                WHERE from_currency = ? AND to_currency = ? AND is_active = 1";
        
        $params = [$fromCurrency, $toCurrency];
        
        if ($date) {
            $sql .= " AND effective_date <= ?";
            $params[] = $date;
        }
        
        $sql .= " ORDER BY effective_date DESC LIMIT 1";
        
        $result = $this->db->fetch($sql, $params);
        
        if ($result) {
            return $result['rate'];
        }
        
        // إذا لم يوجد سعر مباشر، جرب العكس
        $sql = "SELECT rate FROM exchange_rates 
                WHERE from_currency = ? AND to_currency = ? AND is_active = 1";
        
        $params = [$toCurrency, $fromCurrency];
        
        if ($date) {
            $sql .= " AND effective_date <= ?";
            $params[] = $date;
        }
        
        $sql .= " ORDER BY effective_date DESC LIMIT 1";
        
        $result = $this->db->fetch($sql, $params);
        
        if ($result && $result['rate'] > 0) {
            return 1 / $result['rate'];
        }
        
        return null;
    }
    
    /**
     * تحديث سعر الصرف
     */
    public function updateExchangeRate($fromCurrency, $toCurrency, $rate, $userId)
    {
        // إلغاء تفعيل الأسعار القديمة
        $this->db->update('exchange_rates', 
            ['is_active' => 0], 
            'from_currency = ? AND to_currency = ?', 
            [$fromCurrency, $toCurrency]
        );
        
        // إضافة السعر الجديد
        return $this->db->insert('exchange_rates', [
            'from_currency' => $fromCurrency,
            'to_currency' => $toCurrency,
            'rate' => $rate,
            'effective_date' => date('Y-m-d'),
            'created_by_user_id' => $userId,
            'is_active' => 1
        ]);
    }
    
    /**
     * تحويل مبلغ من عملة إلى أخرى
     */
    public function convertAmount($amount, $fromCurrency, $toCurrency, $date = null)
    {
        if ($fromCurrency === $toCurrency) {
            return $amount;
        }
        
        $rate = $this->getExchangeRate($fromCurrency, $toCurrency, $date);
        
        if ($rate === null) {
            throw new Exception("لا يوجد سعر صرف من {$fromCurrency} إلى {$toCurrency}");
        }
        
        return $amount * $rate;
    }
    
    /**
     * تنسيق المبلغ حسب العملة
     */
    public function formatAmount($amount, $currencyCode)
    {
        $currency = $this->findByCode($currencyCode);
        
        if (!$currency) {
            return number_format($amount, 2);
        }
        
        $decimals = $currency['decimal_places'] ?? 2;
        $formatted = number_format($amount, $decimals);
        
        if ($currency['symbol']) {
            return $formatted . ' ' . $currency['symbol'];
        }
        
        return $formatted . ' ' . $currencyCode;
    }
    
    /**
     * التحقق من وجود كود عملة
     */
    public function codeExists($code, $excludeId = null)
    {
        $sql = "SELECT COUNT(*) as count FROM currencies WHERE code = ?";
        $params = [$code];
        
        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        $result = $this->db->fetch($sql, $params);
        return $result['count'] > 0;
    }
    
    /**
     * الحصول على إحصائيات العملات
     */
    public function getStats()
    {
        $sql = "SELECT 
                    COUNT(*) as total_currencies,
                    SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_currencies,
                    (SELECT COUNT(*) FROM exchange_rates WHERE is_active = 1) as active_exchange_rates
                FROM currencies";
        
        return $this->db->fetch($sql);
    }
    
    /**
     * الحصول على العملات الأكثر استخداماً
     */
    public function getMostUsedCurrencies($limit = 5)
    {
        $sql = "SELECT c.*, 
                    (SELECT COUNT(*) FROM transfers WHERE sending_currency_code = c.code OR receiving_currency_code = c.code) as usage_count
                FROM currencies c
                WHERE c.is_active = 1
                ORDER BY usage_count DESC
                LIMIT ?";
        
        return $this->db->fetchAll($sql, [$limit]);
    }
}
