<?php
/**
 * نموذج التحويل
 * Transfer Model
 */

require_once 'BaseModel.php';

class Transfer extends BaseModel
{
    protected $table = 'transfers';
    protected $fillable = [
        'transfer_number', 'sender_customer_id', 'beneficiary_name', 'beneficiary_phone',
        'beneficiary_address', 'beneficiary_city', 'beneficiary_country_id',
        'beneficiary_bank_name', 'beneficiary_account_number', 'beneficiary_iban',
        'sending_amount', 'sending_currency_code', 'receiving_amount', 'receiving_currency_code',
        'exchange_rate', 'fee_amount', 'fee_currency_code', 'total_paid_by_sender',
        'purpose', 'notes', 'status', 'transfer_type', 'sending_branch_id', 'receiving_branch_id',
        'created_by_user_id', 'approved_by_user_id', 'approved_at', 'sent_at', 'received_at', 'paid_at'
    ];
    
    /**
     * البحث عن تحويل برقم التحويل
     */
    public function findByTransferNumber($transferNumber)
    {
        return $this->findBy('transfer_number', $transferNumber);
    }
    
    /**
     * الحصول على تحويل مع جميع التفاصيل
     */
    public function findWithDetails($id)
    {
        $sql = "SELECT t.*, 
                       c.first_name as sender_first_name, c.last_name as sender_last_name,
                       c.phone as sender_phone, c.email as sender_email,
                       sc.name as sending_currency_name, sc.symbol as sending_currency_symbol,
                       rc.name as receiving_currency_name, rc.symbol as receiving_currency_symbol,
                       fc.name as fee_currency_name, fc.symbol as fee_currency_symbol,
                       bc.name as beneficiary_country_name, bc.name_ar as beneficiary_country_name_ar,
                       sb.name as sending_branch_name, sb.code as sending_branch_code,
                       rb.name as receiving_branch_name, rb.code as receiving_branch_code,
                       cu.first_name as created_by_first_name, cu.last_name as created_by_last_name,
                       au.first_name as approved_by_first_name, au.last_name as approved_by_last_name
                FROM transfers t
                LEFT JOIN customers c ON t.sender_customer_id = c.id
                LEFT JOIN currencies sc ON t.sending_currency_code = sc.code
                LEFT JOIN currencies rc ON t.receiving_currency_code = rc.code
                LEFT JOIN currencies fc ON t.fee_currency_code = fc.code
                LEFT JOIN countries bc ON t.beneficiary_country_id = bc.id
                LEFT JOIN branches sb ON t.sending_branch_id = sb.id
                LEFT JOIN branches rb ON t.receiving_branch_id = rb.id
                LEFT JOIN users cu ON t.created_by_user_id = cu.id
                LEFT JOIN users au ON t.approved_by_user_id = au.id
                WHERE t.id = ?";
        
        return $this->db->fetch($sql, [$id]);
    }
    
    /**
     * الحصول على جميع التحويلات مع التفاصيل
     */
    public function getAllWithDetails($conditions = [], $orderBy = 't.created_at DESC', $limit = null)
    {
        $sql = "SELECT t.*, 
                       c.first_name as sender_first_name, c.last_name as sender_last_name,
                       c.phone as sender_phone,
                       sc.symbol as sending_currency_symbol,
                       rc.symbol as receiving_currency_symbol,
                       bc.name as beneficiary_country_name,
                       sb.name as sending_branch_name,
                       rb.name as receiving_branch_name
                FROM transfers t
                LEFT JOIN customers c ON t.sender_customer_id = c.id
                LEFT JOIN currencies sc ON t.sending_currency_code = sc.code
                LEFT JOIN currencies rc ON t.receiving_currency_code = rc.code
                LEFT JOIN countries bc ON t.beneficiary_country_id = bc.id
                LEFT JOIN branches sb ON t.sending_branch_id = sb.id
                LEFT JOIN branches rb ON t.receiving_branch_id = rb.id";
        
        $params = [];
        if (!empty($conditions)) {
            $whereClause = [];
            foreach ($conditions as $column => $value) {
                if (is_array($value)) {
                    $operator = $value[0];
                    $val = $value[1];
                    $whereClause[] = "t.{$column} {$operator} ?";
                    $params[] = $val;
                } else {
                    $whereClause[] = "t.{$column} = ?";
                    $params[] = $value;
                }
            }
            $sql .= " WHERE " . implode(' AND ', $whereClause);
        }
        
        if ($orderBy) {
            $sql .= " ORDER BY {$orderBy}";
        }
        
        if ($limit) {
            $sql .= " LIMIT {$limit}";
        }
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * إنشاء رقم تحويل جديد
     */
    public function generateTransferNumber()
    {
        $prefix = 'TR';
        $date = date('Ymd');
        
        // البحث عن آخر رقم تحويل لليوم
        $sql = "SELECT transfer_number FROM transfers 
                WHERE transfer_number LIKE ? 
                ORDER BY transfer_number DESC 
                LIMIT 1";
        
        $pattern = $prefix . $date . '%';
        $lastTransfer = $this->db->fetch($sql, [$pattern]);
        
        if ($lastTransfer) {
            // استخراج الرقم التسلسلي وزيادته
            $lastNumber = substr($lastTransfer['transfer_number'], -4);
            $newNumber = str_pad((int)$lastNumber + 1, 4, '0', STR_PAD_LEFT);
        } else {
            $newNumber = '0001';
        }
        
        return $prefix . $date . $newNumber;
    }
    
    /**
     * إنشاء تحويل جديد
     */
    public function createTransfer($data)
    {
        // إنشاء رقم التحويل
        $data['transfer_number'] = $this->generateTransferNumber();
        
        return $this->create($data);
    }
    
    /**
     * تحديث حالة التحويل
     */
    public function updateStatus($transferId, $status, $userId = null)
    {
        $updateData = ['status' => $status];
        
        // إضافة timestamps حسب الحالة
        switch ($status) {
            case 'approved':
                $updateData['approved_by_user_id'] = $userId;
                $updateData['approved_at'] = date('Y-m-d H:i:s');
                break;
            case 'sent':
                $updateData['sent_at'] = date('Y-m-d H:i:s');
                break;
            case 'received':
                $updateData['received_at'] = date('Y-m-d H:i:s');
                break;
            case 'paid':
                $updateData['paid_at'] = date('Y-m-d H:i:s');
                break;
        }
        
        return $this->update($transferId, $updateData);
    }
    
    /**
     * البحث في التحويلات
     */
    public function search($query, $limit = 20)
    {
        $sql = "SELECT t.*, 
                       c.first_name as sender_first_name, c.last_name as sender_last_name,
                       sc.symbol as sending_currency_symbol,
                       rc.symbol as receiving_currency_symbol
                FROM transfers t
                LEFT JOIN customers c ON t.sender_customer_id = c.id
                LEFT JOIN currencies sc ON t.sending_currency_code = sc.code
                LEFT JOIN currencies rc ON t.receiving_currency_code = rc.code
                WHERE (t.transfer_number LIKE ? OR t.beneficiary_name LIKE ? 
                       OR c.first_name LIKE ? OR c.last_name LIKE ? OR c.phone LIKE ?)
                ORDER BY t.created_at DESC
                LIMIT ?";
        
        $searchTerm = "%{$query}%";
        return $this->db->fetchAll($sql, [
            $searchTerm, $searchTerm, $searchTerm, $searchTerm, $searchTerm, $limit
        ]);
    }
    
    /**
     * الحصول على التحويلات الصادرة
     */
    public function getOutgoingTransfers($conditions = [], $limit = null)
    {
        $conditions['transfer_type'] = 'outgoing';
        return $this->getAllWithDetails($conditions, 't.created_at DESC', $limit);
    }
    
    /**
     * الحصول على التحويلات الواردة
     */
    public function getIncomingTransfers($conditions = [], $limit = null)
    {
        $conditions['transfer_type'] = 'incoming';
        return $this->getAllWithDetails($conditions, 't.created_at DESC', $limit);
    }
    
    /**
     * الحصول على التحويلات المعلقة للموافقة
     */
    public function getPendingApproval($branchId = null)
    {
        $conditions = ['status' => 'pending'];
        if ($branchId) {
            $conditions['sending_branch_id'] = $branchId;
        }
        return $this->getAllWithDetails($conditions, 't.created_at ASC');
    }
    
    /**
     * الحصول على إحصائيات التحويلات
     */
    public function getStats($dateFrom = null, $dateTo = null, $branchId = null)
    {
        $sql = "SELECT 
                    COUNT(*) as total_transfers,
                    SUM(CASE WHEN status = 'paid' THEN 1 ELSE 0 END) as completed_transfers,
                    SUM(CASE WHEN status IN ('pending', 'approved', 'sent', 'received') THEN 1 ELSE 0 END) as pending_transfers,
                    SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled_transfers,
                    SUM(CASE WHEN status = 'paid' THEN total_paid_by_sender ELSE 0 END) as total_amount,
                    SUM(CASE WHEN status = 'paid' THEN fee_amount ELSE 0 END) as total_fees,
                    SUM(CASE WHEN transfer_type = 'outgoing' THEN 1 ELSE 0 END) as outgoing_count,
                    SUM(CASE WHEN transfer_type = 'incoming' THEN 1 ELSE 0 END) as incoming_count
                FROM transfers 
                WHERE 1=1";
        
        $params = [];
        
        if ($dateFrom) {
            $sql .= " AND DATE(created_at) >= ?";
            $params[] = $dateFrom;
        }
        
        if ($dateTo) {
            $sql .= " AND DATE(created_at) <= ?";
            $params[] = $dateTo;
        }
        
        if ($branchId) {
            $sql .= " AND (sending_branch_id = ? OR receiving_branch_id = ?)";
            $params[] = $branchId;
            $params[] = $branchId;
        }
        
        return $this->db->fetch($sql, $params);
    }
    
    /**
     * الحصول على التحويلات حسب التاريخ
     */
    public function getByDateRange($dateFrom, $dateTo, $conditions = [])
    {
        $conditions['created_at'] = ['>=', $dateFrom . ' 00:00:00'];
        $allConditions = $conditions;
        $allConditions['created_at'] = ['<=', $dateTo . ' 23:59:59'];
        
        return $this->getAllWithDetails($allConditions);
    }
    
    /**
     * الحصول على التحويلات حسب الحالة
     */
    public function getByStatus($status, $branchId = null)
    {
        $conditions = ['status' => $status];
        if ($branchId) {
            $conditions['sending_branch_id'] = $branchId;
        }
        return $this->getAllWithDetails($conditions);
    }
    
    /**
     * الحصول على التحويلات حسب العملة
     */
    public function getByCurrency($currencyCode, $type = 'sending')
    {
        $column = $type === 'sending' ? 'sending_currency_code' : 'receiving_currency_code';
        return $this->getAllWithDetails([$column => $currencyCode]);
    }
}
