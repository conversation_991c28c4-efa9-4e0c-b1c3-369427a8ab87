<?php
/**
 * نقطة الدخول الرئيسية للتطبيق
 * Application Entry Point
 */

// إعداد معالجة الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

// بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// تعيين المنطقة الزمنية
date_default_timezone_set('Asia/Riyadh');

// تعيين ترميز الأحرف
header('Content-Type: text/html; charset=utf-8');

// تحديد مسار الجذر
define('ROOT_PATH', __DIR__);
define('APP_PATH', ROOT_PATH . '/app');
define('PUBLIC_PATH', ROOT_PATH . '/public');
define('STORAGE_PATH', ROOT_PATH . '/storage');

// التحقق من وجود المجلدات المطلوبة
$requiredDirs = [
    STORAGE_PATH . '/logs',
    STORAGE_PATH . '/uploads',
    STORAGE_PATH . '/uploads/documents',
    STORAGE_PATH . '/cache'
];

foreach ($requiredDirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

// تحميل ملفات النواة
require_once APP_PATH . '/Core/Database.php';
require_once APP_PATH . '/Core/Router.php';
require_once APP_PATH . '/Core/Request.php';

// تحميل الدوال المساعدة
require_once APP_PATH . '/Helpers/AuthHelper.php';
require_once APP_PATH . '/Helpers/FormatHelper.php';

// إنشاء موجه الطلبات
$router = new Router();

// تعريف المسارات

// الصفحة الرئيسية
$router->get('/', 'PublicController', 'landing');

// المصادقة
$router->get('/login', 'AuthController', 'showLogin');
$router->post('/login', 'AuthController', 'login');
$router->get('/logout', 'AuthController', 'logout');

// لوحة التحكم
$router->get('/dashboard', 'DashboardController', 'index');

// العملاء
$router->get('/customers', 'CustomerController', 'index');
$router->get('/customers/create', 'CustomerController', 'create');
$router->post('/customers', 'CustomerController', 'store');
$router->get('/customers/{id}', 'CustomerController', 'show');
$router->get('/customers/{id}/edit', 'CustomerController', 'edit');
$router->post('/customers/{id}', 'CustomerController', 'update');

// التحويلات
$router->get('/transfers', 'TransferController', 'index');
$router->get('/transfers/outgoing', 'TransferController', 'outgoing');
$router->get('/transfers/incoming', 'TransferController', 'incoming');
$router->get('/transfers/pending', 'TransferController', 'pendingApproval');
$router->get('/transfers/create', 'TransferController', 'create');
$router->post('/transfers', 'TransferController', 'store');
$router->get('/transfers/{id}', 'TransferController', 'show');
$router->post('/transfers/{id}/approve', 'TransferController', 'approve');
$router->post('/transfers/{id}/reject', 'TransferController', 'reject');

// المستخدمين
$router->get('/users', 'UserController', 'index');
$router->get('/users/create', 'UserController', 'create');
$router->post('/users', 'UserController', 'store');
$router->get('/users/{id}/edit', 'UserController', 'edit');
$router->post('/users/{id}', 'UserController', 'update');

// الإعدادات
$router->get('/settings/currencies', 'SettingsController', 'currencies');
$router->get('/settings/exchange-rates', 'SettingsController', 'exchangeRates');
$router->get('/settings/branches', 'SettingsController', 'branches');
$router->post('/settings/currencies', 'SettingsController', 'storeCurrency');
$router->post('/settings/exchange-rates', 'SettingsController', 'storeExchangeRate');
$router->post('/settings/branches', 'SettingsController', 'storeBranch');

// التقارير
$router->get('/reports/transfers', 'ReportController', 'transfers');
$router->get('/reports/customers', 'ReportController', 'customers');

// الحسابات
$router->get('/accounting/chart-of-accounts', 'AccountController', 'chartOfAccounts');
$router->get('/accounting/journal', 'AccountController', 'journal');
$router->get('/accounting/ledger', 'AccountController', 'ledger');
$router->get('/accounting/ledger/{account_id}', 'AccountController', 'showLedger');
$router->post('/accounting/journal', 'AccountController', 'storeJournalEntry');

// مسارات AJAX
$router->get('/transfers/exchange-rate', 'TransferController', 'getExchangeRate');
$router->get('/customers/search', 'CustomerController', 'search');
$router->get('/accounting/search-accounts', 'AccountController', 'searchAccounts');
$router->get('/auth/check', 'AuthController', 'checkAuth');

try {
    // تشغيل الموجه
    $router->dispatch();
} catch (Exception $e) {
    // معالجة الأخطاء
    error_log($e->getMessage());

    // عرض صفحة خطأ بسيطة
    echo "<!DOCTYPE html>";
    echo "<html lang='ar' dir='rtl'>";
    echo "<head>";
    echo "<meta charset='UTF-8'>";
    echo "<title>خطأ - Trust Plus</title>";
    echo "<style>body{font-family:Arial;text-align:center;padding:50px;}</style>";
    echo "</head>";
    echo "<body>";
    echo "<h1>حدث خطأ في النظام</h1>";
    echo "<p>يرجى المحاولة مرة أخرى أو التواصل مع المدير</p>";
    echo "<p><a href='/Trust_Plus/'>العودة للصفحة الرئيسية</a></p>";
    echo "<hr>";
    echo "<small>تفاصيل الخطأ: " . htmlspecialchars($e->getMessage()) . "</small>";
    echo "</body>";
    echo "</html>";
}
