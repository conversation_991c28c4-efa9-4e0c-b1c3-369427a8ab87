<?php
/**
 * نقطة الدخول الرئيسية للتطبيق
 * Application Entry Point
 */

// إعداد معالجة الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

// بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// تعيين المنطقة الزمنية
date_default_timezone_set('Asia/Riyadh');

// تعيين ترميز الأحرف
header('Content-Type: text/html; charset=utf-8');

// تحديد مسار الجذر
define('ROOT_PATH', __DIR__);
define('APP_PATH', ROOT_PATH . '/app');
define('PUBLIC_PATH', ROOT_PATH . '/public');
define('STORAGE_PATH', ROOT_PATH . '/storage');

// التحقق من وجود المجلدات المطلوبة
$requiredDirs = [
    STORAGE_PATH . '/logs',
    STORAGE_PATH . '/uploads',
    STORAGE_PATH . '/uploads/documents',
    STORAGE_PATH . '/cache'
];

foreach ($requiredDirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

// تحميل ملفات النواة
require_once APP_PATH . '/Core/Database.php';
require_once APP_PATH . '/Core/Router.php';
require_once APP_PATH . '/Core/Request.php';

// تحميل الدوال المساعدة
require_once APP_PATH . '/Helpers/AuthHelper.php';
require_once APP_PATH . '/Helpers/FormatHelper.php';

// نظام routing بسيط باستخدام GET parameters
$route = $_GET['route'] ?? '';
$action = $_GET['action'] ?? '';
$id = $_GET['id'] ?? '';

try {
    switch ($route) {
        case '':
        case 'home':
            require_once APP_PATH . '/Controllers/PublicController.php';
            $controller = new PublicController();
            $controller->landing();
            break;

        case 'login':
            require_once APP_PATH . '/Controllers/AuthController.php';
            $controller = new AuthController();
            if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                $controller->login();
            } else {
                $controller->showLogin();
            }
            break;

        case 'logout':
            require_once APP_PATH . '/Controllers/AuthController.php';
            $controller = new AuthController();
            $controller->logout();
            break;

        case 'dashboard':
            require_once APP_PATH . '/Controllers/DashboardController.php';
            $controller = new DashboardController();
            $controller->index();
            break;

        case 'customers':
            require_once APP_PATH . '/Controllers/CustomerController.php';
            $controller = new CustomerController();
            switch ($action) {
                case 'create':
                    $controller->create();
                    break;
                case 'store':
                    $controller->store();
                    break;
                case 'show':
                    $controller->show($id);
                    break;
                case 'edit':
                    $controller->edit($id);
                    break;
                case 'update':
                    $controller->update($id);
                    break;
                default:
                    $controller->index();
            }
            break;

        case 'transfers':
            require_once APP_PATH . '/Controllers/TransferController.php';
            $controller = new TransferController();
            switch ($action) {
                case 'create':
                    $controller->create();
                    break;
                case 'store':
                    $controller->store();
                    break;
                case 'show':
                    $controller->show($id);
                    break;
                case 'outgoing':
                    $controller->outgoing();
                    break;
                case 'incoming':
                    $controller->incoming();
                    break;
                case 'pending':
                    $controller->pendingApproval();
                    break;
                default:
                    $controller->index();
            }
            break;

        default:
            // الصفحة الرئيسية الافتراضية
            require_once APP_PATH . '/Controllers/PublicController.php';
            $controller = new PublicController();
            $controller->landing();
            break;
    }
} catch (Exception $e) {
    // معالجة الأخطاء
    error_log($e->getMessage());

    // عرض صفحة خطأ بسيطة
    echo "<!DOCTYPE html>";
    echo "<html lang='ar' dir='rtl'>";
    echo "<head>";
    echo "<meta charset='UTF-8'>";
    echo "<title>خطأ - Trust Plus</title>";
    echo "<style>body{font-family:Arial;text-align:center;padding:50px;color:#333;}</style>";
    echo "</head>";
    echo "<body>";
    echo "<h1>🚫 حدث خطأ في النظام</h1>";
    echo "<p>يرجى المحاولة مرة أخرى أو التواصل مع المدير</p>";
    echo "<p><a href='/Trust_plus/' style='color:#007bff;text-decoration:none;'>🏠 العودة للصفحة الرئيسية</a></p>";
    echo "<hr>";
    echo "<details style='margin-top:20px;'>";
    echo "<summary>تفاصيل الخطأ (للمطورين)</summary>";
    echo "<pre style='text-align:left;background:#f8f9fa;padding:10px;border-radius:5px;'>";
    echo htmlspecialchars($e->getMessage());
    echo "\n\nFile: " . $e->getFile();
    echo "\nLine: " . $e->getLine();
    echo "</pre>";
    echo "</details>";
    echo "</body>";
    echo "</html>";
}
