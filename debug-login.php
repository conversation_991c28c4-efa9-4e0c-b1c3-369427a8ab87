<?php
/**
 * تشخيص مشاكل تسجيل الدخول
 */

// بدء الجلسة
session_start();

// تحديد مسار الجذر
define('ROOT_PATH', __DIR__);
define('APP_PATH', ROOT_PATH . '/app');

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>تشخيص تسجيل الدخول - Trust Plus</title>";
echo "<style>";
echo "body { font-family: Arial; margin: 20px; background: #f5f5f5; }";
echo ".container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; }";
echo ".success { color: #28a745; } .error { color: #dc3545; } .info { color: #007bff; }";
echo ".section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }";
echo "pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }";
echo "</style>";
echo "</head>";
echo "<body>";
echo "<div class='container'>";

echo "<h1>🔍 تشخيص مشاكل تسجيل الدخول</h1>";

// 1. اختبار الملفات
echo "<div class='section'>";
echo "<h3>📁 اختبار الملفات المطلوبة</h3>";
$files = [
    'index.php' => 'ملف الدخول الرئيسي',
    'app/Controllers/AuthController.php' => 'وحدة تحكم المصادقة',
    'app/Helpers/AuthHelper.php' => 'مساعد المصادقة',
    'app/Views/auth/login.php' => 'صفحة تسجيل الدخول'
];

foreach ($files as $file => $desc) {
    $exists = file_exists($file);
    $status = $exists ? '<span class="success">✅</span>' : '<span class="error">❌</span>';
    echo "<p>$desc: $status</p>";
}
echo "</div>";

// 2. اختبار قاعدة البيانات
echo "<div class='section'>";
echo "<h3>🗄️ اختبار قاعدة البيانات</h3>";
try {
    $config = require 'app/Config/database.php';
    $dsn = "mysql:host={$config['host']};dbname={$config['dbname']};charset={$config['charset']}";
    $pdo = new PDO($dsn, $config['username'], $config['password'], $config['options']);
    echo '<p class="success">✅ الاتصال بقاعدة البيانات نجح</p>';
    
    // اختبار المستخدم الافتراضي
    $stmt = $pdo->query("SELECT * FROM users WHERE username = 'admin'");
    $user = $stmt->fetch();
    
    if ($user) {
        echo '<p class="success">✅ المستخدم الافتراضي موجود</p>';
        echo "<p><strong>معرف المستخدم:</strong> {$user['id']}</p>";
        echo "<p><strong>اسم المستخدم:</strong> {$user['username']}</p>";
        echo "<p><strong>البريد الإلكتروني:</strong> {$user['email']}</p>";
        echo "<p><strong>الاسم:</strong> {$user['first_name']} {$user['last_name']}</p>";
        echo "<p><strong>الحالة:</strong> " . ($user['is_active'] ? 'مفعل' : 'غير مفعل') . "</p>";
        
        // اختبار كلمة المرور
        $testPassword = 'password';
        $passwordMatch = password_verify($testPassword, $user['password_hash']);
        echo "<p><strong>كلمة المرور 'password':</strong> " . ($passwordMatch ? '<span class="success">✅ صحيحة</span>' : '<span class="error">❌ خاطئة</span>') . "</p>";
        
    } else {
        echo '<p class="error">❌ المستخدم الافتراضي غير موجود</p>';
    }
    
} catch (Exception $e) {
    echo '<p class="error">❌ خطأ في قاعدة البيانات: ' . $e->getMessage() . '</p>';
}
echo "</div>";

// 3. اختبار الجلسة
echo "<div class='section'>";
echo "<h3>🔐 اختبار الجلسة</h3>";
echo "<p><strong>معرف الجلسة:</strong> " . session_id() . "</p>";
echo "<p><strong>بيانات الجلسة:</strong></p>";
echo "<pre>" . print_r($_SESSION, true) . "</pre>";
echo "</div>";

// 4. اختبار المتغيرات
echo "<div class='section'>";
echo "<h3>🌐 متغيرات الخادم</h3>";
echo "<p><strong>REQUEST_METHOD:</strong> " . ($_SERVER['REQUEST_METHOD'] ?? 'غير محدد') . "</p>";
echo "<p><strong>REQUEST_URI:</strong> " . ($_SERVER['REQUEST_URI'] ?? 'غير محدد') . "</p>";
echo "<p><strong>HTTP_HOST:</strong> " . ($_SERVER['HTTP_HOST'] ?? 'غير محدد') . "</p>";
echo "<p><strong>SCRIPT_NAME:</strong> " . ($_SERVER['SCRIPT_NAME'] ?? 'غير محدد') . "</p>";
echo "</div>";

// 5. اختبار GET parameters
echo "<div class='section'>";
echo "<h3>📥 متغيرات GET</h3>";
echo "<pre>" . print_r($_GET, true) . "</pre>";
echo "</div>";

// 6. اختبار POST parameters
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<div class='section'>";
    echo "<h3>📤 متغيرات POST</h3>";
    echo "<pre>" . print_r($_POST, true) . "</pre>";
    echo "</div>";
}

// 7. روابط الاختبار
echo "<div class='section'>";
echo "<h3>🔗 روابط الاختبار</h3>";
echo "<p><a href='index.php' style='color: #007bff;'>🏠 الصفحة الرئيسية</a></p>";
echo "<p><a href='index.php?route=login' style='color: #007bff;'>🔐 صفحة تسجيل الدخول</a></p>";
echo "<p><a href='login-test.php' style='color: #007bff;'>🧪 اختبار تسجيل الدخول</a></p>";
echo "<p><a href='quick-test.php' style='color: #007bff;'>⚡ الاختبار السريع</a></p>";
echo "</div>";

// 8. نموذج اختبار مباشر
echo "<div class='section'>";
echo "<h3>🎯 اختبار مباشر</h3>";
echo "<form method='POST' action='index.php?route=login'>";
echo "<p><input type='text' name='username' value='admin' placeholder='اسم المستخدم' style='padding: 8px; margin: 5px;'></p>";
echo "<p><input type='password' name='password' value='password' placeholder='كلمة المرور' style='padding: 8px; margin: 5px;'></p>";
echo "<p><button type='submit' style='padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px;'>تسجيل الدخول</button></p>";
echo "</form>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
