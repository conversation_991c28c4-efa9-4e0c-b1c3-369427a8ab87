<?php
/**
 * وحدة تحكم الحسابات (المحاسبة)
 * Accounting Controller
 */

require_once 'BaseController.php';

class AccountController extends BaseController
{
    private $accountModel;
    private $journalEntryModel;
    private $currencyModel;
    
    public function __construct()
    {
        parent::__construct();
        $this->accountModel = new Account();
        $this->journalEntryModel = new JournalEntry();
        $this->currencyModel = new Currency();
    }
    
    /**
     * عرض شجرة الحسابات
     */
    public function chartOfAccounts()
    {
        $this->requirePermission('accounting_view');
        
        try {
            $currencyCode = $this->request->get('currency', 'SAR');
            $accounts = $this->accountModel->getAllAccountsWithBalances($currencyCode);
            $currencies = $this->currencyModel->getActiveCurrencies();
            
            $this->view('accounting/chart_of_accounts', [
                'title' => 'شجرة الحسابات',
                'accounts' => $accounts,
                'currencies' => $currencies,
                'selectedCurrency' => $currencyCode,
                'flash' => $this->getFlashMessage()
            ]);
            
        } catch (Exception $e) {
            $this->handleError($e, 'حدث خطأ في تحميل شجرة الحسابات');
        }
    }
    
    /**
     * عرض قيود اليومية
     */
    public function journal()
    {
        $this->requirePermission('accounting_view');
        
        try {
            $page = $this->request->get('page', 1);
            $dateFrom = $this->request->get('date_from', '');
            $dateTo = $this->request->get('date_to', '');
            $approved = $this->request->get('approved', '');
            $perPage = 20;
            
            $conditions = [];
            
            if (!empty($dateFrom)) {
                $conditions['entry_date'] = ['>=', $dateFrom];
            }
            
            if (!empty($dateTo)) {
                $conditions['entry_date'] = ['<=', $dateTo];
            }
            
            if ($approved !== '') {
                $conditions['is_approved'] = (int)$approved;
            }
            
            $result = $this->journalEntryModel->paginate($page, $perPage, $conditions, 'entry_date DESC, id DESC');
            $entries = $result['data'];
            $pagination = $result;
            
            // إضافة تفاصيل القيود
            foreach ($entries as &$entry) {
                $entry['details'] = $this->journalEntryModel->getEntryDetails($entry['id']);
            }
            
            $this->view('accounting/journal', [
                'title' => 'قيود اليومية',
                'entries' => $entries,
                'pagination' => $pagination,
                'dateFrom' => $dateFrom,
                'dateTo' => $dateTo,
                'approved' => $approved,
                'flash' => $this->getFlashMessage()
            ]);
            
        } catch (Exception $e) {
            $this->handleError($e, 'حدث خطأ في تحميل قيود اليومية');
        }
    }
    
    /**
     * عرض صفحة دفتر الأستاذ
     */
    public function ledger()
    {
        $this->requirePermission('accounting_view');
        
        try {
            $accounts = $this->accountModel->getChartOfAccounts();
            $currencies = $this->currencyModel->getActiveCurrencies();
            
            $this->view('accounting/ledger', [
                'title' => 'دفتر الأستاذ',
                'accounts' => $accounts,
                'currencies' => $currencies,
                'flash' => $this->getFlashMessage()
            ]);
            
        } catch (Exception $e) {
            $this->handleError($e, 'حدث خطأ في تحميل صفحة دفتر الأستاذ');
        }
    }
    
    /**
     * عرض دفتر الأستاذ لحساب معين
     */
    public function showLedger($accountId)
    {
        $this->requirePermission('accounting_view');
        
        try {
            $account = $this->accountModel->find($accountId);
            
            if (!$account) {
                $this->redirect('/accounting/ledger', 'الحساب غير موجود', 'error');
            }
            
            $currencyCode = $this->request->get('currency', 'SAR');
            $dateFrom = $this->request->get('date_from', '');
            $dateTo = $this->request->get('date_to', '');
            
            // الحصول على حركات الحساب
            $ledgerEntries = $this->accountModel->getLedger($accountId, $currencyCode, $dateFrom, $dateTo);
            
            // الحصول على رصيد الحساب
            $balance = $this->accountModel->getAccountWithBalance($accountId, $currencyCode);
            
            $this->view('accounting/show_ledger', [
                'title' => 'دفتر الأستاذ - ' . $account['account_name_ar'],
                'account' => $account,
                'balance' => $balance,
                'ledgerEntries' => $ledgerEntries,
                'currencyCode' => $currencyCode,
                'dateFrom' => $dateFrom,
                'dateTo' => $dateTo,
                'flash' => $this->getFlashMessage()
            ]);
            
        } catch (Exception $e) {
            $this->handleError($e, 'حدث خطأ في تحميل دفتر الأستاذ');
        }
    }
    
    /**
     * إنشاء قيد يومية جديد
     */
    public function storeJournalEntry()
    {
        $this->requirePermission('accounting_create');
        
        // التحقق من صحة البيانات
        $this->validate([
            'entry_date' => 'required',
            'description' => 'required|min:5',
            'details' => 'required'
        ]);
        
        try {
            $entryData = [
                'entry_date' => $this->request->get('entry_date'),
                'description' => $this->request->get('description'),
                'reference_type' => $this->request->get('reference_type', 'manual'),
                'reference_id' => $this->request->get('reference_id'),
                'created_by_user_id' => AuthHelper::getCurrentUserId()
            ];
            
            $details = json_decode($this->request->get('details'), true);
            
            if (!is_array($details) || empty($details)) {
                $this->redirect('/accounting/journal', 'تفاصيل القيد مطلوبة', 'error');
            }
            
            // التحقق من صحة تفاصيل القيد
            foreach ($details as $detail) {
                if (empty($detail['account_id']) || empty($detail['currency_code'])) {
                    $this->redirect('/accounting/journal', 'بيانات القيد غير مكتملة', 'error');
                }
                
                if (empty($detail['debit_amount']) && empty($detail['credit_amount'])) {
                    $this->redirect('/accounting/journal', 'يجب إدخال مبلغ مدين أو دائن', 'error');
                }
            }
            
            // إنشاء القيد
            $entryId = $this->journalEntryModel->createJournalEntry($entryData, $details);
            
            // الموافقة التلقائية إذا كان المستخدم لديه صلاحية
            if (AuthHelper::hasPermission('accounting_approve')) {
                $this->journalEntryModel->approveEntry($entryId, AuthHelper::getCurrentUserId());
            }
            
            // تسجيل النشاط
            $this->logActivity('journal_entry_create', "إنشاء قيد يومية: {$entryData['description']}", $entryId, 'journal_entry');
            
            $this->redirect('/accounting/journal', 'تم إنشاء القيد بنجاح', 'success');
            
        } catch (Exception $e) {
            $this->handleError($e, 'حدث خطأ أثناء إنشاء القيد');
        }
    }
    
    /**
     * الموافقة على قيد يومية
     */
    public function approveJournalEntry($id)
    {
        $this->requirePermission('accounting_approve');
        
        try {
            $entry = $this->journalEntryModel->find($id);
            
            if (!$entry) {
                $this->json(['success' => false, 'message' => 'القيد غير موجود'], 404);
            }
            
            if ($entry['is_approved']) {
                $this->json(['success' => false, 'message' => 'القيد معتمد مسبقاً'], 400);
            }
            
            $this->journalEntryModel->approveEntry($id, AuthHelper::getCurrentUserId());
            
            // تسجيل النشاط
            $this->logActivity('journal_entry_approve', "اعتماد قيد يومية رقم: {$entry['entry_number']}", $id, 'journal_entry');
            
            $this->json([
                'success' => true,
                'message' => 'تم اعتماد القيد بنجاح'
            ]);
            
        } catch (Exception $e) {
            $this->json(['success' => false, 'message' => 'حدث خطأ أثناء اعتماد القيد'], 500);
        }
    }
    
    /**
     * إلغاء الموافقة على قيد يومية
     */
    public function unapproveJournalEntry($id)
    {
        $this->requirePermission('accounting_approve');
        
        try {
            $entry = $this->journalEntryModel->find($id);
            
            if (!$entry) {
                $this->json(['success' => false, 'message' => 'القيد غير موجود'], 404);
            }
            
            if (!$entry['is_approved']) {
                $this->json(['success' => false, 'message' => 'القيد غير معتمد'], 400);
            }
            
            $this->journalEntryModel->unapproveEntry($id);
            
            // تسجيل النشاط
            $this->logActivity('journal_entry_unapprove', "إلغاء اعتماد قيد يومية رقم: {$entry['entry_number']}", $id, 'journal_entry');
            
            $this->json([
                'success' => true,
                'message' => 'تم إلغاء اعتماد القيد'
            ]);
            
        } catch (Exception $e) {
            $this->json(['success' => false, 'message' => 'حدث خطأ أثناء إلغاء اعتماد القيد'], 500);
        }
    }
    
    /**
     * البحث في الحسابات (AJAX)
     */
    public function searchAccounts()
    {
        $this->requirePermission('accounting_view');
        
        try {
            $query = $this->request->get('q', '');
            
            if (strlen($query) < 2) {
                $this->json(['success' => false, 'message' => 'يجب إدخال حرفين على الأقل']);
            }
            
            $accounts = $this->accountModel->search($query);
            
            $this->json([
                'success' => true,
                'accounts' => $accounts
            ]);
            
        } catch (Exception $e) {
            $this->json(['success' => false, 'message' => 'حدث خطأ في البحث'], 500);
        }
    }
    
    /**
     * تحديث أرصدة الحسابات
     */
    public function updateBalances()
    {
        $this->requirePermission('accounting_manage');
        
        try {
            $accounts = $this->accountModel->all();
            $currencies = $this->currencyModel->getActiveCurrencies();
            
            $updatedCount = 0;
            
            foreach ($accounts as $account) {
                foreach ($currencies as $currency) {
                    $this->accountModel->updateAccountBalance($account['id'], $currency['code']);
                    $updatedCount++;
                }
            }
            
            // تسجيل النشاط
            $this->logActivity('balances_update', "تحديث أرصدة الحسابات - {$updatedCount} رصيد", null, 'system');
            
            $this->json([
                'success' => true,
                'message' => "تم تحديث {$updatedCount} رصيد بنجاح"
            ]);
            
        } catch (Exception $e) {
            $this->json(['success' => false, 'message' => 'حدث خطأ أثناء تحديث الأرصدة'], 500);
        }
    }
}
