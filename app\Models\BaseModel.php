<?php
/**
 * النموذج الأساسي
 * Base Model Class
 */

abstract class BaseModel
{
    protected $db;
    protected $table;
    protected $primaryKey = 'id';
    protected $fillable = [];
    protected $timestamps = true;
    
    public function __construct()
    {
        $this->db = Database::getInstance();
    }
    
    /**
     * البحث عن سجل بالمعرف
     */
    public function find($id)
    {
        $sql = "SELECT * FROM {$this->table} WHERE {$this->primaryKey} = ?";
        return $this->db->fetch($sql, [$id]);
    }
    
    /**
     * البحث عن سجل بشرط معين
     */
    public function findBy($column, $value)
    {
        $sql = "SELECT * FROM {$this->table} WHERE {$column} = ?";
        return $this->db->fetch($sql, [$value]);
    }
    
    /**
     * الحصول على جميع السجلات
     */
    public function all($orderBy = null, $limit = null)
    {
        $sql = "SELECT * FROM {$this->table}";
        
        if ($orderBy) {
            $sql .= " ORDER BY {$orderBy}";
        }
        
        if ($limit) {
            $sql .= " LIMIT {$limit}";
        }
        
        return $this->db->fetchAll($sql);
    }
    
    /**
     * البحث بشروط متعددة
     */
    public function where($conditions, $orderBy = null, $limit = null)
    {
        $whereClause = [];
        $params = [];
        
        foreach ($conditions as $column => $value) {
            if (is_array($value)) {
                $operator = $value[0];
                $val = $value[1];
                $whereClause[] = "{$column} {$operator} ?";
                $params[] = $val;
            } else {
                $whereClause[] = "{$column} = ?";
                $params[] = $value;
            }
        }
        
        $sql = "SELECT * FROM {$this->table} WHERE " . implode(' AND ', $whereClause);
        
        if ($orderBy) {
            $sql .= " ORDER BY {$orderBy}";
        }
        
        if ($limit) {
            $sql .= " LIMIT {$limit}";
        }
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * إنشاء سجل جديد
     */
    public function create($data)
    {
        // تصفية البيانات حسب الحقول المسموحة
        $filteredData = $this->filterFillable($data);
        
        // إضافة timestamps إذا كانت مفعلة
        if ($this->timestamps) {
            $filteredData['created_at'] = date('Y-m-d H:i:s');
            $filteredData['updated_at'] = date('Y-m-d H:i:s');
        }
        
        return $this->db->insert($this->table, $filteredData);
    }
    
    /**
     * تحديث سجل
     */
    public function update($id, $data)
    {
        // تصفية البيانات حسب الحقول المسموحة
        $filteredData = $this->filterFillable($data);
        
        // إضافة timestamp للتحديث إذا كانت مفعلة
        if ($this->timestamps) {
            $filteredData['updated_at'] = date('Y-m-d H:i:s');
        }
        
        $where = "{$this->primaryKey} = ?";
        return $this->db->update($this->table, $filteredData, $where, [$id]);
    }
    
    /**
     * حذف سجل
     */
    public function delete($id)
    {
        $where = "{$this->primaryKey} = ?";
        return $this->db->delete($this->table, $where, [$id]);
    }
    
    /**
     * عد السجلات
     */
    public function count($conditions = [])
    {
        $sql = "SELECT COUNT(*) as count FROM {$this->table}";
        $params = [];
        
        if (!empty($conditions)) {
            $whereClause = [];
            foreach ($conditions as $column => $value) {
                $whereClause[] = "{$column} = ?";
                $params[] = $value;
            }
            $sql .= " WHERE " . implode(' AND ', $whereClause);
        }
        
        $result = $this->db->fetch($sql, $params);
        return $result['count'];
    }
    
    /**
     * البحث مع الترقيم
     */
    public function paginate($page = 1, $perPage = 20, $conditions = [], $orderBy = null)
    {
        $offset = ($page - 1) * $perPage;
        
        // بناء الاستعلام
        $sql = "SELECT * FROM {$this->table}";
        $countSql = "SELECT COUNT(*) as count FROM {$this->table}";
        $params = [];
        
        if (!empty($conditions)) {
            $whereClause = [];
            foreach ($conditions as $column => $value) {
                if (is_array($value)) {
                    $operator = $value[0];
                    $val = $value[1];
                    $whereClause[] = "{$column} {$operator} ?";
                    $params[] = $val;
                } else {
                    $whereClause[] = "{$column} = ?";
                    $params[] = $value;
                }
            }
            $whereString = " WHERE " . implode(' AND ', $whereClause);
            $sql .= $whereString;
            $countSql .= $whereString;
        }
        
        if ($orderBy) {
            $sql .= " ORDER BY {$orderBy}";
        }
        
        $sql .= " LIMIT {$perPage} OFFSET {$offset}";
        
        // الحصول على البيانات والعدد الإجمالي
        $data = $this->db->fetchAll($sql, $params);
        $totalResult = $this->db->fetch($countSql, $params);
        $total = $totalResult['count'];
        
        return [
            'data' => $data,
            'total' => $total,
            'per_page' => $perPage,
            'current_page' => $page,
            'last_page' => ceil($total / $perPage),
            'from' => $offset + 1,
            'to' => min($offset + $perPage, $total)
        ];
    }
    
    /**
     * تصفية البيانات حسب الحقول المسموحة
     */
    protected function filterFillable($data)
    {
        if (empty($this->fillable)) {
            return $data;
        }
        
        $filtered = [];
        foreach ($this->fillable as $field) {
            if (isset($data[$field])) {
                $filtered[$field] = $data[$field];
            }
        }
        
        return $filtered;
    }
    
    /**
     * تنفيذ استعلام مخصص
     */
    public function query($sql, $params = [])
    {
        return $this->db->query($sql, $params);
    }
    
    /**
     * الحصول على نتيجة واحدة من استعلام مخصص
     */
    public function fetchQuery($sql, $params = [])
    {
        return $this->db->fetch($sql, $params);
    }
    
    /**
     * الحصول على نتائج متعددة من استعلام مخصص
     */
    public function fetchAllQuery($sql, $params = [])
    {
        return $this->db->fetchAll($sql, $params);
    }
}
