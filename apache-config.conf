# إعدادات Apache لنظام Trust Plus
# ضع هذا الملف في مجلد sites-available في Apache

<VirtualHost *:80>
    ServerName trustplus.local
    DocumentRoot /path/to/Trust_Plus
    
    # إعدادات المجلد الرئيسي
    <Directory /path/to/Trust_Plus>
        Options -Indexes +FollowSymLinks
        AllowOverride All
        Require all granted
    </Directory>
    
    # إعدادات مجلد public
    <Directory /path/to/Trust_Plus/public>
        Options -Indexes +FollowSymLinks
        AllowOverride All
        Require all granted
    </Directory>
    
    # منع الوصول لمجلدات النظام
    <Directory /path/to/Trust_Plus/app>
        Require all denied
    </Directory>
    
    <Directory /path/to/Trust_Plus/storage> يخرج لي
        Require all denied
    </Directory>
    
    # إعدادات PHP
    php_admin_value upload_max_filesize 5M
    php_admin_value post_max_size 5M
    php_admin_value max_execution_time 300
    php_admin_value memory_limit 256M
    php_admin_value display_errors Off
    php_admin_value log_errors On
    
    # سجلات الأخطاء
    ErrorLog ${APACHE_LOG_DIR}/trustplus_error.log
    CustomLog ${APACHE_LOG_DIR}/trustplus_access.log combined
</VirtualHost>

# إعدادات HTTPS (اختياري)
<VirtualHost *:443>
    ServerName trustplus.local
    DocumentRoot /path/to/Trust_Plus
    
    SSLEngine on
    SSLCertificateFile /path/to/certificate.crt
    SSLCertificateKeyFile /path/to/private.key
    
    # نفس إعدادات HTTP
    <Directory /path/to/Trust_Plus>
        Options -Indexes +FollowSymLinks
        AllowOverride All
        Require all granted
    </Directory>
    
    <Directory /path/to/Trust_Plus/public>
        Options -Indexes +FollowSymLinks
        AllowOverride All
        Require all granted
    </Directory>
    
    <Directory /path/to/Trust_Plus/app>
        Require all denied
    </Directory>
    
    <Directory /path/to/Trust_Plus/storage>
        Require all denied
    </Directory>
</VirtualHost>
