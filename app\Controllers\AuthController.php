<?php
/**
 * وحدة تحكم المصادقة
 * Authentication Controller
 */

require_once 'BaseController.php';

class AuthController extends BaseController
{
    private $userModel;
    
    public function __construct()
    {
        parent::__construct();
        $this->userModel = new User();
    }
    
    /**
     * عرض صفحة تسجيل الدخول
     */
    public function showLogin()
    {
        // إذا كان المستخدم مسجل دخول، إعادة توجيه للوحة التحكم
        if (AuthHelper::isLoggedIn()) {
            $this->redirect('/dashboard');
        }
        
        // التحقق من remember token
        AuthHelper::checkRememberToken();
        if (AuthHelper::isLoggedIn()) {
            $this->redirect('/dashboard');
        }
        
        $this->view('auth/login', [
            'title' => 'تسجيل الدخول',
            'flash' => $this->getFlashMessage(),
            'errors' => $this->getValidationErrors(),
            'old' => $this->getOldInput()
        ]);
    }
    
    /**
     * معالجة تسجيل الدخول
     */
    public function login()
    {
        // التحقق من صحة البيانات
        $this->validate([
            'username' => 'required',
            'password' => 'required|min:6'
        ]);
        
        $username = $this->request->get('username');
        $password = $this->request->get('password');
        $remember = $this->request->get('remember') ? true : false;
        
        try {
            // البحث عن المستخدم
            $user = $this->userModel->findByUsername($username);
            
            if (!$user) {
                // جرب البحث بالبريد الإلكتروني
                $user = $this->userModel->findByEmail($username);
            }
            
            // التحقق من وجود المستخدم وكلمة المرور
            if (!$user || !AuthHelper::verifyPassword($password, $user['password_hash'])) {
                $this->redirect('/login', 'اسم المستخدم أو كلمة المرور غير صحيحة', 'error');
            }
            
            // التحقق من تفعيل المستخدم
            if (!$user['is_active']) {
                $this->redirect('/login', 'حسابك غير مفعل، يرجى التواصل مع المدير', 'error');
            }
            
            // تسجيل الدخول
            AuthHelper::login($user['id'], $remember);
            
            // تحديث آخر تسجيل دخول
            $this->userModel->updateLastLogin($user['id']);
            
            // تسجيل النشاط
            $this->logActivity('login', 'تسجيل دخول المستخدم', $user['id'], 'user');
            
            // إعادة التوجيه
            $redirectTo = $_SESSION['intended_url'] ?? '/dashboard';
            unset($_SESSION['intended_url']);
            
            $this->redirect($redirectTo, 'مرحباً بك في نظام Trust Plus', 'success');
            
        } catch (Exception $e) {
            $this->handleError($e, 'حدث خطأ أثناء تسجيل الدخول');
        }
    }
    
    /**
     * تسجيل الخروج
     */
    public function logout()
    {
        $user = $this->getCurrentUser();
        
        if ($user) {
            // تسجيل النشاط
            $this->logActivity('logout', 'تسجيل خروج المستخدم', $user['id'], 'user');
        }
        
        // تسجيل الخروج
        AuthHelper::logout();
        
        $this->redirect('/login', 'تم تسجيل الخروج بنجاح', 'success');
    }
    
    /**
     * عرض صفحة تغيير كلمة المرور
     */
    public function showChangePassword()
    {
        $this->requireLogin();
        
        $this->view('auth/change_password', [
            'title' => 'تغيير كلمة المرور',
            'user' => $this->getCurrentUser(),
            'flash' => $this->getFlashMessage(),
            'errors' => $this->getValidationErrors()
        ]);
    }
    
    /**
     * معالجة تغيير كلمة المرور
     */
    public function changePassword()
    {
        $this->requireLogin();
        
        // التحقق من صحة البيانات
        $this->validate([
            'current_password' => 'required',
            'new_password' => 'required|min:8',
            'confirm_password' => 'required'
        ]);
        
        $currentPassword = $this->request->get('current_password');
        $newPassword = $this->request->get('new_password');
        $confirmPassword = $this->request->get('confirm_password');
        
        try {
            $user = $this->getCurrentUser();
            
            // التحقق من كلمة المرور الحالية
            if (!AuthHelper::verifyPassword($currentPassword, $user['password_hash'])) {
                $this->redirect('/auth/change-password', 'كلمة المرور الحالية غير صحيحة', 'error');
            }
            
            // التحقق من تطابق كلمة المرور الجديدة
            if ($newPassword !== $confirmPassword) {
                $this->redirect('/auth/change-password', 'كلمة المرور الجديدة غير متطابقة', 'error');
            }
            
            // تحديث كلمة المرور
            $this->userModel->updatePassword($user['id'], $newPassword);
            
            // تسجيل النشاط
            $this->logActivity('password_change', 'تغيير كلمة المرور', $user['id'], 'user');
            
            $this->redirect('/dashboard', 'تم تغيير كلمة المرور بنجاح', 'success');
            
        } catch (Exception $e) {
            $this->handleError($e, 'حدث خطأ أثناء تغيير كلمة المرور');
        }
    }
    
    /**
     * عرض صفحة نسيان كلمة المرور
     */
    public function showForgotPassword()
    {
        $this->view('auth/forgot_password', [
            'title' => 'نسيت كلمة المرور',
            'flash' => $this->getFlashMessage(),
            'errors' => $this->getValidationErrors()
        ]);
    }
    
    /**
     * معالجة طلب إعادة تعيين كلمة المرور
     */
    public function forgotPassword()
    {
        // التحقق من صحة البيانات
        $this->validate([
            'email' => 'required|email'
        ]);
        
        $email = $this->request->get('email');
        
        try {
            $user = $this->userModel->findByEmail($email);
            
            if (!$user) {
                // لا نكشف عن عدم وجود المستخدم لأسباب أمنية
                $this->redirect('/auth/forgot-password', 'إذا كان البريد الإلكتروني موجود، ستصلك رسالة إعادة تعيين كلمة المرور', 'info');
            }
            
            // إنشاء token إعادة التعيين
            $resetToken = bin2hex(random_bytes(32));
            $expiresAt = date('Y-m-d H:i:s', strtotime('+1 hour'));
            
            // حفظ token في قاعدة البيانات
            $db = Database::getInstance();
            $db->insert('password_resets', [
                'email' => $email,
                'token' => $resetToken,
                'expires_at' => $expiresAt,
                'created_at' => date('Y-m-d H:i:s')
            ]);
            
            // إرسال البريد الإلكتروني (يمكن تنفيذه لاحقاً)
            // $this->sendResetEmail($email, $resetToken);
            
            // تسجيل النشاط
            $this->logActivity('password_reset_request', 'طلب إعادة تعيين كلمة المرور', $user['id'], 'user');
            
            $this->redirect('/auth/forgot-password', 'تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني', 'success');
            
        } catch (Exception $e) {
            $this->handleError($e, 'حدث خطأ أثناء معالجة الطلب');
        }
    }
    
    /**
     * التحقق من حالة تسجيل الدخول (للـ AJAX)
     */
    public function checkAuth()
    {
        $this->json([
            'authenticated' => AuthHelper::isLoggedIn(),
            'user' => AuthHelper::isLoggedIn() ? $this->getCurrentUser() : null
        ]);
    }
    
    /**
     * تسجيل الخروج من جميع الأجهزة
     */
    public function logoutAllDevices()
    {
        $this->requireLogin();
        
        try {
            $user = $this->getCurrentUser();
            
            // حذف جميع remember tokens
            $this->userModel->update($user['id'], ['remember_token' => null]);
            
            // تسجيل النشاط
            $this->logActivity('logout_all_devices', 'تسجيل الخروج من جميع الأجهزة', $user['id'], 'user');
            
            // تسجيل الخروج من الجلسة الحالية
            AuthHelper::logout();
            
            $this->redirect('/login', 'تم تسجيل الخروج من جميع الأجهزة بنجاح', 'success');
            
        } catch (Exception $e) {
            $this->handleError($e, 'حدث خطأ أثناء تسجيل الخروج');
        }
    }
}
