# Trust Plus - نظام إدارة التحويلات المالية

نظام متكامل لإدارة التحويلات المالية مع نظام محاسبة متقدم مبني بـ PHP و MySQL.

## المميزات الرئيسية

### 🔄 إدارة التحويلات
- إنشاء وإدارة التحويلات الصادرة والواردة
- نظام موافقة متقدم للتحويلات
- تتبع حالة التحويلات في الوقت الفعلي
- دعم العملات المتعددة مع أسعار الصرف

### 👥 إدارة العملاء
- قاعدة بيانات شاملة للعملاء
- حفظ الوثائق والمستندات
- تتبع تاريخ التحويلات لكل عميل
- إحصائيات مفصلة عن العملاء

### 💰 نظام المحاسبة
- شجرة حسابات متكاملة
- قيود اليومية التلقائية والدليلة
- دفتر الأستاذ لكل حساب
- تقارير مالية شاملة

### 🏢 إدارة الفروع والمستخدمين
- نظام فروع متعدد
- إدارة المستخدمين والصلاحيات
- أدوار مخصصة لكل مستخدم
- تسجيل جميع الأنشطة

### 📊 التقارير والإحصائيات
- تقارير مفصلة عن التحويلات
- إحصائيات الأداء المالي
- رسوم بيانية تفاعلية
- إمكانية التصدير والطباعة

## متطلبات النظام

- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- Apache/Nginx مع mod_rewrite
- مساحة تخزين 100MB على الأقل

## التثبيت

### 1. تحميل الملفات
```bash
git clone https://github.com/your-repo/trust-plus.git
cd trust-plus
```

### 2. إعداد قاعدة البيانات
1. إنشاء قاعدة بيانات جديدة في MySQL
2. استيراد ملف `database.sql`
3. تحديث إعدادات قاعدة البيانات في `app/Config/database.php`

### 3. إعداد الخادم
1. توجيه الخادم إلى مجلد `public`
2. التأكد من تفعيل mod_rewrite
3. تعيين صلاحيات الكتابة لمجلد `storage`

### 4. الإعدادات الأساسية
1. تحديث `app/Config/app.php` حسب بيئة العمل
2. تعيين المنطقة الزمنية والعملة الافتراضية

## بيانات الدخول الافتراضية

- **اسم المستخدم:** admin
- **كلمة المرور:** password
- **البريد الإلكتروني:** <EMAIL>

⚠️ **مهم:** يجب تغيير كلمة المرور فور تسجيل الدخول الأول

## هيكل المشروع

```
Trust_Plus/
├── app/                      # الشيفرة المصدرية
│   ├── Controllers/          # وحدات التحكم
│   ├── Models/              # نماذج البيانات
│   ├── Views/               # ملفات العرض
│   ├── Helpers/             # دوال مساعدة
│   ├── Core/                # النواة الأساسية
│   └── Config/              # ملفات الإعدادات
├── public/                  # المجلد العام
│   ├── index.php           # نقطة الدخول
│   ├── css/                # ملفات CSS
│   ├── js/                 # ملفات JavaScript
│   └── images/             # الصور
├── storage/                 # ملفات التخزين
│   ├── logs/               # سجلات النظام
│   ├── uploads/            # الملفات المرفوعة
│   └── cache/              # ملفات التخزين المؤقت
├── database.sql            # هيكل قاعدة البيانات
└── .htaccess              # إعدادات Apache
```

## الاستخدام

### إنشاء تحويل جديد
1. الانتقال إلى "التحويلات" > "إنشاء تحويل جديد"
2. اختيار العميل المرسل أو إضافة عميل جديد
3. إدخال بيانات المستفيد
4. تحديد المبلغ والعملة
5. مراجعة التفاصيل وحفظ التحويل

### إدارة العملاء
1. الانتقال إلى "العملاء"
2. إضافة عميل جديد أو تعديل عميل موجود
3. رفع الوثائق المطلوبة
4. عرض تاريخ التحويلات

### النظام المحاسبي
1. عرض شجرة الحسابات من "المحاسبة" > "شجرة الحسابات"
2. مراجعة قيود اليومية من "المحاسبة" > "قيود اليومية"
3. عرض دفتر الأستاذ لأي حساب

## الصلاحيات والأدوار

### مدير النظام (Admin)
- جميع الصلاحيات
- إدارة المستخدمين والأدوار
- إعدادات النظام

### مدير الفرع (Manager)
- إدارة التحويلات والعملاء
- الموافقة على التحويلات
- عرض التقارير

### أمين الصندوق (Cashier)
- إنشاء التحويلات
- إدارة العملاء
- عرض التحويلات

### المحاسب (Accountant)
- إدارة النظام المحاسبي
- إنشاء القيود
- عرض التقارير المالية

### المستعلم (Viewer)
- عرض البيانات فقط
- لا يمكن التعديل أو الحذف

## الأمان

- تشفير كلمات المرور باستخدام bcrypt
- حماية من هجمات SQL Injection
- نظام صلاحيات متقدم
- تسجيل جميع الأنشطة
- حماية الملفات الحساسة

## النسخ الاحتياطي

### نسخ احتياطي لقاعدة البيانات
```bash
mysqldump -u username -p trust_plus > backup_$(date +%Y%m%d).sql
```

### نسخ احتياطي للملفات
```bash
tar -czf trust_plus_files_$(date +%Y%m%d).tar.gz /path/to/trust_plus
```

## استكشاف الأخطاء

### مشاكل شائعة

1. **خطأ في الاتصال بقاعدة البيانات**
   - التحقق من إعدادات قاعدة البيانات
   - التأكد من تشغيل خدمة MySQL

2. **خطأ 404 في الصفحات**
   - التحقق من تفعيل mod_rewrite
   - مراجعة ملف .htaccess

3. **مشاكل في رفع الملفات**
   - التحقق من صلاحيات مجلد storage
   - مراجعة إعدادات PHP لحجم الملفات

### سجلات الأخطاء
- سجلات النظام: `storage/logs/app.log`
- سجلات PHP: حسب إعدادات الخادم
- سجلات Apache/Nginx: حسب إعدادات الخادم

## التطوير والمساهمة

### إضافة ميزة جديدة
1. إنشاء Controller جديد في `app/Controllers/`
2. إنشاء Model إذا لزم الأمر في `app/Models/`
3. إنشاء Views في `app/Views/`
4. إضافة المسارات في `public/index.php`

### معايير الكود
- استخدام PSR-4 لتحميل الفئات
- تعليقات باللغة العربية والإنجليزية
- اتباع معايير PHP الحديثة

## الدعم والمساعدة

للحصول على الدعم أو الإبلاغ عن مشاكل:
- البريد الإلكتروني: <EMAIL>
- الهاتف: +966-11-1234567

## الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف LICENSE للتفاصيل.

## إصدارات النظام

### الإصدار 1.0.0 (الحالي)
- النظام الأساسي للتحويلات
- إدارة العملاء
- النظام المحاسبي
- التقارير الأساسية

### خطط مستقبلية
- تطبيق الهاتف المحمول
- API للتكامل مع أنظمة أخرى
- نظام إشعارات متقدم
- تقارير أكثر تفصيلاً

---

**Trust Plus** - نظام إدارة التحويلات المالية المتكامل
© 2024 جميع الحقوق محفوظة
