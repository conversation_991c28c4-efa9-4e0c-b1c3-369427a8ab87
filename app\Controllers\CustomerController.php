<?php
/**
 * وحدة تحكم العملاء
 * Customer Controller
 */

require_once 'BaseController.php';

class CustomerController extends BaseController
{
    private $customerModel;
    private $countryModel;
    
    public function __construct()
    {
        parent::__construct();
        $this->customerModel = new Customer();
        $this->countryModel = new Country();
    }
    
    /**
     * عرض قائمة العملاء
     */
    public function index()
    {
        $this->requirePermission('customers_view');
        
        try {
            $page = $this->request->get('page', 1);
            $search = $this->request->get('search', '');
            $perPage = 20;
            
            if (!empty($search)) {
                $customers = $this->customerModel->search($search, 50);
                $pagination = null;
            } else {
                $result = $this->customerModel->paginate($page, $perPage);
                $customers = $result['data'];
                $pagination = $result;
            }
            
            // إضافة تفاصيل العملاء
            foreach ($customers as &$customer) {
                $customer = $this->customerModel->findWithDetails($customer['id']);
            }
            
            $this->view('customers/index', [
                'title' => 'إدارة العملاء',
                'customers' => $customers,
                'pagination' => $pagination,
                'search' => $search,
                'flash' => $this->getFlashMessage()
            ]);
            
        } catch (Exception $e) {
            $this->handleError($e, 'حدث خطأ في تحميل قائمة العملاء');
        }
    }
    
    /**
     * عرض صفحة إضافة عميل جديد
     */
    public function create()
    {
        $this->requirePermission('customers_create');
        
        try {
            $countries = $this->countryModel->getActiveCountries();
            
            $this->view('customers/create', [
                'title' => 'إضافة عميل جديد',
                'countries' => $countries,
                'flash' => $this->getFlashMessage(),
                'errors' => $this->getValidationErrors(),
                'old' => $this->getOldInput()
            ]);
            
        } catch (Exception $e) {
            $this->handleError($e, 'حدث خطأ في تحميل صفحة إضافة العميل');
        }
    }
    
    /**
     * حفظ عميل جديد
     */
    public function store()
    {
        $this->requirePermission('customers_create');
        
        // التحقق من صحة البيانات
        $this->validate([
            'first_name' => 'required|min:2',
            'last_name' => 'required|min:2',
            'phone' => 'required|min:10',
            'nationality_country_id' => 'required|numeric',
            'country_id' => 'required|numeric'
        ]);
        
        try {
            $data = $this->request->only([
                'first_name', 'last_name', 'phone', 'email', 'national_id', 'passport_number',
                'date_of_birth', 'nationality_country_id', 'address', 'city', 'country_id'
            ]);
            
            // التحقق من عدم تكرار رقم الهاتف
            if ($this->customerModel->phoneExists($data['phone'])) {
                $this->redirect('/customers/create', 'رقم الهاتف مستخدم من قبل', 'error');
            }
            
            // التحقق من عدم تكرار الهوية الوطنية
            if (!empty($data['national_id']) && $this->customerModel->nationalIdExists($data['national_id'])) {
                $this->redirect('/customers/create', 'رقم الهوية الوطنية مستخدم من قبل', 'error');
            }
            
            // التحقق من عدم تكرار رقم الجواز
            if (!empty($data['passport_number']) && $this->customerModel->passportExists($data['passport_number'])) {
                $this->redirect('/customers/create', 'رقم الجواز مستخدم من قبل', 'error');
            }
            
            // إضافة معرف المستخدم الذي أنشأ العميل
            $data['created_by_user_id'] = AuthHelper::getCurrentUserId();
            
            // تنسيق رقم الهاتف
            $data['phone'] = FormatHelper::formatPhone($data['phone']);
            
            // إنشاء العميل
            $customerId = $this->customerModel->create($data);
            
            // تسجيل النشاط
            $this->logActivity('customer_create', "إنشاء عميل جديد: {$data['first_name']} {$data['last_name']}", $customerId, 'customer');
            
            $this->redirect('/customers', 'تم إضافة العميل بنجاح', 'success');
            
        } catch (Exception $e) {
            $this->handleError($e, 'حدث خطأ أثناء إضافة العميل');
        }
    }
    
    /**
     * عرض تفاصيل عميل
     */
    public function show($id)
    {
        $this->requirePermission('customers_view');
        
        try {
            $customer = $this->customerModel->findWithDetails($id);
            
            if (!$customer) {
                $this->redirect('/customers', 'العميل غير موجود', 'error');
            }
            
            // الحصول على تحويلات العميل
            $transfers = $this->customerModel->getTransfers($id, 20);
            
            // الحصول على إحصائيات العميل
            $stats = $this->customerModel->getCustomerStats($id);
            
            // الحصول على وثائق العميل
            $documents = $this->customerModel->getDocuments($id);
            
            $this->view('customers/show', [
                'title' => 'تفاصيل العميل',
                'customer' => $customer,
                'transfers' => $transfers,
                'stats' => $stats,
                'documents' => $documents,
                'flash' => $this->getFlashMessage()
            ]);
            
        } catch (Exception $e) {
            $this->handleError($e, 'حدث خطأ في تحميل تفاصيل العميل');
        }
    }
    
    /**
     * عرض صفحة تعديل عميل
     */
    public function edit($id)
    {
        $this->requirePermission('customers_edit');
        
        try {
            $customer = $this->customerModel->findWithDetails($id);
            
            if (!$customer) {
                $this->redirect('/customers', 'العميل غير موجود', 'error');
            }
            
            $countries = $this->countryModel->getActiveCountries();
            
            $this->view('customers/edit', [
                'title' => 'تعديل العميل',
                'customer' => $customer,
                'countries' => $countries,
                'flash' => $this->getFlashMessage(),
                'errors' => $this->getValidationErrors(),
                'old' => $this->getOldInput()
            ]);
            
        } catch (Exception $e) {
            $this->handleError($e, 'حدث خطأ في تحميل صفحة تعديل العميل');
        }
    }
    
    /**
     * تحديث بيانات عميل
     */
    public function update($id)
    {
        $this->requirePermission('customers_edit');
        
        // التحقق من صحة البيانات
        $this->validate([
            'first_name' => 'required|min:2',
            'last_name' => 'required|min:2',
            'phone' => 'required|min:10',
            'nationality_country_id' => 'required|numeric',
            'country_id' => 'required|numeric'
        ]);
        
        try {
            $customer = $this->customerModel->find($id);
            
            if (!$customer) {
                $this->redirect('/customers', 'العميل غير موجود', 'error');
            }
            
            $data = $this->request->only([
                'first_name', 'last_name', 'phone', 'email', 'national_id', 'passport_number',
                'date_of_birth', 'nationality_country_id', 'address', 'city', 'country_id'
            ]);
            
            // التحقق من عدم تكرار رقم الهاتف
            if ($this->customerModel->phoneExists($data['phone'], $id)) {
                $this->redirect("/customers/{$id}/edit", 'رقم الهاتف مستخدم من قبل', 'error');
            }
            
            // التحقق من عدم تكرار الهوية الوطنية
            if (!empty($data['national_id']) && $this->customerModel->nationalIdExists($data['national_id'], $id)) {
                $this->redirect("/customers/{$id}/edit", 'رقم الهوية الوطنية مستخدم من قبل', 'error');
            }
            
            // التحقق من عدم تكرار رقم الجواز
            if (!empty($data['passport_number']) && $this->customerModel->passportExists($data['passport_number'], $id)) {
                $this->redirect("/customers/{$id}/edit", 'رقم الجواز مستخدم من قبل', 'error');
            }
            
            // تنسيق رقم الهاتف
            $data['phone'] = FormatHelper::formatPhone($data['phone']);
            
            // تحديث العميل
            $this->customerModel->update($id, $data);
            
            // تسجيل النشاط
            $this->logActivity('customer_update', "تحديث بيانات العميل: {$data['first_name']} {$data['last_name']}", $id, 'customer');
            
            $this->redirect("/customers/{$id}", 'تم تحديث بيانات العميل بنجاح', 'success');
            
        } catch (Exception $e) {
            $this->handleError($e, 'حدث خطأ أثناء تحديث بيانات العميل');
        }
    }
    
    /**
     * البحث في العملاء (AJAX)
     */
    public function search()
    {
        $this->requirePermission('customers_view');
        
        try {
            $query = $this->request->get('q', '');
            $limit = $this->request->get('limit', 10);
            
            if (strlen($query) < 2) {
                $this->json(['success' => false, 'message' => 'يجب إدخال حرفين على الأقل']);
            }
            
            $customers = $this->customerModel->search($query, $limit);
            
            $this->json([
                'success' => true,
                'customers' => $customers
            ]);
            
        } catch (Exception $e) {
            $this->json(['success' => false, 'message' => 'حدث خطأ في البحث'], 500);
        }
    }
    
    /**
     * رفع وثيقة للعميل
     */
    public function uploadDocument($id)
    {
        $this->requirePermission('customers_edit');
        
        try {
            $customer = $this->customerModel->find($id);
            
            if (!$customer) {
                $this->json(['success' => false, 'message' => 'العميل غير موجود'], 404);
            }
            
            // التحقق من وجود ملف
            if (!$this->request->hasFile('document')) {
                $this->json(['success' => false, 'message' => 'لم يتم اختيار ملف'], 400);
            }
            
            // رفع الملف
            $filePath = $this->handleFileUpload('document', 'documents/');
            
            // حفظ بيانات الوثيقة
            $documentData = [
                'document_type' => $this->request->get('document_type', 'other'),
                'document_number' => $this->request->get('document_number', ''),
                'file_path' => $filePath,
                'uploaded_by_user_id' => AuthHelper::getCurrentUserId()
            ];
            
            $documentId = $this->customerModel->addDocument($id, $documentData);
            
            // تسجيل النشاط
            $this->logActivity('customer_document_upload', "رفع وثيقة للعميل: {$customer['first_name']} {$customer['last_name']}", $id, 'customer');
            
            $this->json([
                'success' => true,
                'message' => 'تم رفع الوثيقة بنجاح',
                'document_id' => $documentId
            ]);
            
        } catch (Exception $e) {
            $this->json(['success' => false, 'message' => 'حدث خطأ أثناء رفع الوثيقة'], 500);
        }
    }
}
