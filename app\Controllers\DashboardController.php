<?php
/**
 * وحدة تحكم لوحة التحكم
 * Dashboard Controller
 */

require_once 'BaseController.php';

class DashboardController extends BaseController
{
    private $transferModel;
    private $customerModel;
    private $userModel;
    private $accountModel;
    
    public function __construct()
    {
        parent::__construct();
        $this->transferModel = new Transfer();
        $this->customerModel = new Customer();
        $this->userModel = new User();
        $this->accountModel = new Account();
    }
    
    /**
     * عرض لوحة التحكم الرئيسية
     */
    public function index()
    {
        $this->requireLogin();
        
        try {
            $user = $this->getCurrentUser();
            $branchId = $user['branch_id'];
            
            // الحصول على الإحصائيات العامة
            $stats = $this->getDashboardStats($branchId);
            
            // الحصول على التحويلات الأخيرة
            $recentTransfers = $this->getRecentTransfers($branchId, 10);
            
            // الحصول على التحويلات المعلقة للموافقة
            $pendingTransfers = $this->getPendingTransfers($branchId);
            
            // الحصول على العملاء الجدد
            $newCustomers = $this->getNewCustomers($branchId, 5);
            
            // الحصول على إحصائيات الأداء الشهري
            $monthlyStats = $this->getMonthlyStats($branchId);
            
            // الحصول على أهم العملات
            $topCurrencies = $this->getTopCurrencies($branchId);
            
            $this->view('dashboard/index', [
                'title' => 'لوحة التحكم',
                'user' => $user,
                'stats' => $stats,
                'recentTransfers' => $recentTransfers,
                'pendingTransfers' => $pendingTransfers,
                'newCustomers' => $newCustomers,
                'monthlyStats' => $monthlyStats,
                'topCurrencies' => $topCurrencies,
                'flash' => $this->getFlashMessage()
            ]);
            
        } catch (Exception $e) {
            $this->handleError($e, 'حدث خطأ في تحميل لوحة التحكم');
        }
    }
    
    /**
     * الحصول على إحصائيات لوحة التحكم
     */
    private function getDashboardStats($branchId = null)
    {
        $today = date('Y-m-d');
        $thisMonth = date('Y-m');
        $lastMonth = date('Y-m', strtotime('-1 month'));
        
        // إحصائيات التحويلات
        $transferStats = $this->transferModel->getStats($today, $today, $branchId);
        $monthlyTransferStats = $this->transferModel->getStats($thisMonth . '-01', $today, $branchId);
        $lastMonthTransferStats = $this->transferModel->getStats($lastMonth . '-01', date('Y-m-t', strtotime('-1 month')), $branchId);
        
        // إحصائيات العملاء
        $customerStats = $this->customerModel->getStats();
        
        // إحصائيات المستخدمين (للمديرين فقط)
        $userStats = null;
        if (AuthHelper::hasPermission('users_view')) {
            $userStats = $this->userModel->getStats();
        }
        
        return [
            'today_transfers' => $transferStats['total_transfers'],
            'today_amount' => $transferStats['total_amount'],
            'today_fees' => $transferStats['total_fees'],
            'pending_transfers' => $transferStats['pending_transfers'],
            
            'monthly_transfers' => $monthlyTransferStats['total_transfers'],
            'monthly_amount' => $monthlyTransferStats['total_amount'],
            'monthly_fees' => $monthlyTransferStats['total_fees'],
            'completed_transfers' => $monthlyTransferStats['completed_transfers'],
            
            'last_month_transfers' => $lastMonthTransferStats['total_transfers'],
            'last_month_amount' => $lastMonthTransferStats['total_amount'],
            
            'total_customers' => $customerStats['total_customers'],
            'active_customers' => $customerStats['active_customers'],
            'new_customers_last_month' => $customerStats['new_customers_last_month'],
            
            'users' => $userStats
        ];
    }
    
    /**
     * الحصول على التحويلات الأخيرة
     */
    private function getRecentTransfers($branchId = null, $limit = 10)
    {
        $conditions = [];
        if ($branchId) {
            // إذا كان هناك فرع محدد، اعرض تحويلات الفرع فقط
            $conditions = ['sending_branch_id' => $branchId];
        }
        
        return $this->transferModel->getAllWithDetails($conditions, 't.created_at DESC', $limit);
    }
    
    /**
     * الحصول على التحويلات المعلقة للموافقة
     */
    private function getPendingTransfers($branchId = null)
    {
        if (!AuthHelper::hasPermission('transfers_approve')) {
            return [];
        }
        
        return $this->transferModel->getPendingApproval($branchId);
    }
    
    /**
     * الحصول على العملاء الجدد
     */
    private function getNewCustomers($branchId = null, $limit = 5)
    {
        $conditions = [];
        if ($branchId) {
            // يمكن تصفية العملاء حسب المستخدم الذي أنشأهم من نفس الفرع
            $branchUsers = $this->userModel->getByBranch($branchId);
            $userIds = array_column($branchUsers, 'id');
            if (!empty($userIds)) {
                $conditions['created_by_user_id'] = ['IN', $userIds];
            }
        }
        
        return $this->customerModel->getAllWithDetails($conditions);
    }
    
    /**
     * الحصول على إحصائيات الأداء الشهري
     */
    private function getMonthlyStats($branchId = null)
    {
        $months = [];
        $transferCounts = [];
        $amounts = [];
        
        // الحصول على بيانات آخر 6 أشهر
        for ($i = 5; $i >= 0; $i--) {
            $month = date('Y-m', strtotime("-{$i} months"));
            $monthStart = $month . '-01';
            $monthEnd = date('Y-m-t', strtotime($monthStart));
            
            $stats = $this->transferModel->getStats($monthStart, $monthEnd, $branchId);
            
            $months[] = date('M Y', strtotime($monthStart));
            $transferCounts[] = $stats['completed_transfers'];
            $amounts[] = $stats['total_amount'];
        }
        
        return [
            'months' => $months,
            'transfer_counts' => $transferCounts,
            'amounts' => $amounts
        ];
    }
    
    /**
     * الحصول على أهم العملات المستخدمة
     */
    private function getTopCurrencies($branchId = null)
    {
        $sql = "SELECT 
                    c.code, c.name, c.symbol,
                    COUNT(t.id) as transfer_count,
                    SUM(CASE WHEN t.status = 'paid' THEN t.sending_amount ELSE 0 END) as total_amount
                FROM currencies c
                LEFT JOIN transfers t ON (c.code = t.sending_currency_code OR c.code = t.receiving_currency_code)";
        
        $params = [];
        $whereConditions = ['c.is_active = 1'];
        
        if ($branchId) {
            $whereConditions[] = "(t.sending_branch_id = ? OR t.receiving_branch_id = ?)";
            $params[] = $branchId;
            $params[] = $branchId;
        }
        
        $sql .= " WHERE " . implode(' AND ', $whereConditions);
        $sql .= " GROUP BY c.id HAVING transfer_count > 0 ORDER BY transfer_count DESC LIMIT 5";
        
        $db = Database::getInstance();
        return $db->fetchAll($sql, $params);
    }
    
    /**
     * الحصول على بيانات الرسم البياني للتحويلات اليومية
     */
    public function getTransferChart()
    {
        $this->requireLogin();
        
        try {
            $user = $this->getCurrentUser();
            $branchId = $user['branch_id'];
            $days = $this->request->get('days', 30);
            
            $data = [];
            $labels = [];
            
            for ($i = $days - 1; $i >= 0; $i--) {
                $date = date('Y-m-d', strtotime("-{$i} days"));
                $stats = $this->transferModel->getStats($date, $date, $branchId);
                
                $labels[] = date('M d', strtotime($date));
                $data[] = $stats['completed_transfers'];
            }
            
            $this->json([
                'success' => true,
                'labels' => $labels,
                'data' => $data
            ]);
            
        } catch (Exception $e) {
            $this->json(['success' => false, 'message' => 'حدث خطأ في تحميل البيانات'], 500);
        }
    }
    
    /**
     * الحصول على إحصائيات سريعة للـ AJAX
     */
    public function getQuickStats()
    {
        $this->requireLogin();
        
        try {
            $user = $this->getCurrentUser();
            $branchId = $user['branch_id'];
            
            $today = date('Y-m-d');
            $stats = $this->transferModel->getStats($today, $today, $branchId);
            
            $this->json([
                'success' => true,
                'stats' => [
                    'today_transfers' => $stats['total_transfers'],
                    'today_amount' => FormatHelper::formatMoney($stats['total_amount']),
                    'pending_transfers' => $stats['pending_transfers'],
                    'completed_transfers' => $stats['completed_transfers']
                ]
            ]);
            
        } catch (Exception $e) {
            $this->json(['success' => false, 'message' => 'حدث خطأ في تحميل الإحصائيات'], 500);
        }
    }
}
