<?php
/**
 * دوال مساعدة للتنسيق
 * Format Helper Functions
 */

class FormatHelper
{
    /**
     * تنسيق المبلغ المالي
     */
    public static function formatMoney($amount, $currency = 'SAR', $decimals = 2)
    {
        $formatted = number_format($amount, $decimals);
        
        $symbols = [
            'SAR' => 'ر.س',
            'USD' => '$',
            'EUR' => '€',
            'GBP' => '£',
            'AED' => 'د.إ',
            'KWD' => 'د.ك',
            'BHD' => 'د.ب',
            'QAR' => 'ر.ق',
            'OMR' => 'ر.ع',
            'JOD' => 'د.أ',
            'EGP' => 'ج.م',
            'LBP' => 'ل.ل',
            'SYP' => 'ل.س',
            'IQD' => 'د.ع',
            'YER' => 'ر.ي',
            'MAD' => 'د.م',
            'TND' => 'د.ت',
            'DZD' => 'د.ج',
            'LYD' => 'د.ل',
            'SDG' => 'ج.س',
            'SOS' => 'ش.ص',
            'DJF' => 'ف.ج',
            'KMF' => 'ف.ق',
            'MRU' => 'أ.م'
        ];
        
        $symbol = isset($symbols[$currency]) ? $symbols[$currency] : $currency;
        
        return $formatted . ' ' . $symbol;
    }
    
    /**
     * تنسيق التاريخ
     */
    public static function formatDate($date, $format = 'Y-m-d')
    {
        if (empty($date)) {
            return '';
        }
        
        if (is_string($date)) {
            $date = new DateTime($date);
        }
        
        return $date->format($format);
    }
    
    /**
     * تنسيق التاريخ والوقت
     */
    public static function formatDateTime($datetime, $format = 'Y-m-d H:i:s')
    {
        return self::formatDate($datetime, $format);
    }
    
    /**
     * تنسيق التاريخ بالعربية
     */
    public static function formatDateArabic($date)
    {
        if (empty($date)) {
            return '';
        }
        
        if (is_string($date)) {
            $date = new DateTime($date);
        }
        
        $months = [
            1 => 'يناير', 2 => 'فبراير', 3 => 'مارس', 4 => 'أبريل',
            5 => 'مايو', 6 => 'يونيو', 7 => 'يوليو', 8 => 'أغسطس',
            9 => 'سبتمبر', 10 => 'أكتوبر', 11 => 'نوفمبر', 12 => 'ديسمبر'
        ];
        
        $day = $date->format('d');
        $month = $months[(int)$date->format('m')];
        $year = $date->format('Y');
        
        return "{$day} {$month} {$year}";
    }
    
    /**
     * تنسيق رقم الهاتف
     */
    public static function formatPhone($phone, $countryCode = '+966')
    {
        if (empty($phone)) {
            return '';
        }
        
        // إزالة الأحرف غير الرقمية
        $phone = preg_replace('/[^0-9]/', '', $phone);
        
        // إضافة رمز الدولة إذا لم يكن موجود
        if (!str_starts_with($phone, '966') && !str_starts_with($phone, '+966')) {
            if (str_starts_with($phone, '0')) {
                $phone = '966' . substr($phone, 1);
            } else {
                $phone = '966' . $phone;
            }
        }
        
        return '+' . $phone;
    }
    
    /**
     * تنسيق حالة التحويل
     */
    public static function formatTransferStatus($status)
    {
        $statuses = [
            'pending' => 'في الانتظار',
            'approved' => 'معتمد',
            'sent' => 'مرسل',
            'received' => 'مستلم',
            'paid' => 'مدفوع',
            'cancelled' => 'ملغي',
            'rejected' => 'مرفوض'
        ];
        
        return isset($statuses[$status]) ? $statuses[$status] : $status;
    }
    
    /**
     * تنسيق حالة التحويل مع الألوان
     */
    public static function formatTransferStatusBadge($status)
    {
        $classes = [
            'pending' => 'badge-warning',
            'approved' => 'badge-info',
            'sent' => 'badge-primary',
            'received' => 'badge-success',
            'paid' => 'badge-success',
            'cancelled' => 'badge-secondary',
            'rejected' => 'badge-danger'
        ];
        
        $class = isset($classes[$status]) ? $classes[$status] : 'badge-secondary';
        $text = self::formatTransferStatus($status);
        
        return "<span class=\"badge {$class}\">{$text}</span>";
    }
    
    /**
     * تنسيق نوع التحويل
     */
    public static function formatTransferType($type)
    {
        $types = [
            'outgoing' => 'صادر',
            'incoming' => 'وارد'
        ];
        
        return isset($types[$type]) ? $types[$type] : $type;
    }
    
    /**
     * تقصير النص
     */
    public static function truncate($text, $length = 50, $suffix = '...')
    {
        if (mb_strlen($text) <= $length) {
            return $text;
        }
        
        return mb_substr($text, 0, $length) . $suffix;
    }
    
    /**
     * تنسيق حجم الملف
     */
    public static function formatFileSize($bytes)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }
    
    /**
     * تنظيف وتنسيق النص
     */
    public static function clean($text)
    {
        return htmlspecialchars(trim($text), ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * تحويل النص إلى slug
     */
    public static function slug($text)
    {
        $text = preg_replace('/[^a-zA-Z0-9\s]/', '', $text);
        $text = preg_replace('/\s+/', '-', trim($text));
        return strtolower($text);
    }
}
