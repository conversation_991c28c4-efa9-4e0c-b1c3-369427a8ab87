<?php
/**
 * فئة التوجيه
 * Router Class
 */

class Router
{
    private $routes = [];
    private $currentRoute = null;
    
    public function get($path, $controller, $method)
    {
        $this->addRoute('GET', $path, $controller, $method);
    }
    
    public function post($path, $controller, $method)
    {
        $this->addRoute('POST', $path, $controller, $method);
    }
    
    private function addRoute($httpMethod, $path, $controller, $method)
    {
        $this->routes[] = [
            'method' => $httpMethod,
            'path' => $path,
            'controller' => $controller,
            'action' => $method
        ];
    }
    
    public function dispatch()
    {
        $requestMethod = $_SERVER['REQUEST_METHOD'];
        $requestUri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        
        // إزالة المسار الأساسي للتطبيق
        $basePath = '/Trust_Plus';
        if (strpos($requestUri, $basePath) === 0) {
            $requestUri = substr($requestUri, strlen($basePath));
        }
        
        if (empty($requestUri) || $requestUri === '/') {
            $requestUri = '/';
        }
        
        foreach ($this->routes as $route) {
            if ($this->matchRoute($route, $requestMethod, $requestUri)) {
                $this->currentRoute = $route;
                return $this->callController($route);
            }
        }
        
        // إذا لم يتم العثور على المسار
        $this->handleNotFound();
    }
    
    private function matchRoute($route, $method, $uri)
    {
        if ($route['method'] !== $method) {
            return false;
        }
        
        // تحويل المسار إلى regex للمعاملات
        $pattern = preg_replace('/\{([^}]+)\}/', '([^/]+)', $route['path']);
        $pattern = '#^' . $pattern . '$#';
        
        return preg_match($pattern, $uri);
    }
    
    private function callController($route)
    {
        $controllerName = $route['controller'];
        $methodName = $route['action'];
        
        $controllerFile = __DIR__ . "/../Controllers/{$controllerName}.php";
        
        if (!file_exists($controllerFile)) {
            throw new Exception("Controller file not found: {$controllerFile}");
        }
        
        require_once $controllerFile;
        
        if (!class_exists($controllerName)) {
            throw new Exception("Controller class not found: {$controllerName}");
        }
        
        $controller = new $controllerName();
        
        if (!method_exists($controller, $methodName)) {
            throw new Exception("Method {$methodName} not found in {$controllerName}");
        }
        
        // استخراج المعاملات من URL
        $params = $this->extractParams($route['path'], $_SERVER['REQUEST_URI']);
        
        return call_user_func_array([$controller, $methodName], $params);
    }
    
    private function extractParams($routePath, $requestUri)
    {
        // إزالة المسار الأساسي
        $basePath = '/Trust_Plus';
        if (strpos($requestUri, $basePath) === 0) {
            $requestUri = substr($requestUri, strlen($basePath));
        }
        
        $routeParts = explode('/', trim($routePath, '/'));
        $uriParts = explode('/', trim($requestUri, '/'));
        
        $params = [];
        for ($i = 0; $i < count($routeParts); $i++) {
            if (isset($routeParts[$i]) && preg_match('/\{([^}]+)\}/', $routeParts[$i])) {
                if (isset($uriParts[$i])) {
                    $params[] = $uriParts[$i];
                }
            }
        }
        
        return $params;
    }
    
    private function handleNotFound()
    {
        http_response_code(404);
        echo "404 - Page Not Found";
    }
}
