<?php
/**
 * اختبار سريع للنظام
 */

// تحديد مسار الجذر
define('ROOT_PATH', __DIR__);
define('APP_PATH', ROOT_PATH . '/app');

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>اختبار سريع - Trust Plus</title>";
echo "<style>";
echo "body { font-family: Arial; margin: 40px; background: #f5f5f5; }";
echo ".container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; }";
echo ".success { color: #28a745; } .error { color: #dc3545; } .info { color: #007bff; }";
echo ".btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }";
echo "</style>";
echo "</head>";
echo "<body>";
echo "<div class='container'>";

echo "<h1>🚀 اختبار سريع لنظام Trust Plus</h1>";

// اختبار PHP
echo "<h3>✅ PHP</h3>";
echo "<p>إصدار PHP: <strong>" . phpversion() . "</strong></p>";

// اختبار الملفات
echo "<h3>📁 الملفات الأساسية</h3>";
$files = [
    'index.php' => 'ملف الدخول الرئيسي',
    'app/Core/Database.php' => 'فئة قاعدة البيانات',
    'app/Controllers/AuthController.php' => 'وحدة تحكم المصادقة'
];

foreach ($files as $file => $desc) {
    $exists = file_exists($file);
    $status = $exists ? '<span class="success">✅</span>' : '<span class="error">❌</span>';
    echo "<p>$desc: $status</p>";
}

// اختبار قاعدة البيانات
echo "<h3>🗄️ قاعدة البيانات</h3>";
try {
    $config = require 'app/Config/database.php';
    $dsn = "mysql:host={$config['host']};dbname={$config['dbname']};charset={$config['charset']}";
    $pdo = new PDO($dsn, $config['username'], $config['password'], $config['options']);
    echo '<p class="success">✅ الاتصال نجح</p>';
    
    // اختبار المستخدم الافتراضي
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users WHERE username = 'admin'");
    $result = $stmt->fetch();
    $adminExists = $result['count'] > 0;
    echo "<p>المستخدم الافتراضي: " . ($adminExists ? '<span class="success">✅ موجود</span>' : '<span class="error">❌ غير موجود</span>') . "</p>";
    
} catch (Exception $e) {
    echo '<p class="error">❌ خطأ: ' . $e->getMessage() . '</p>';
}

// روابط الاختبار
echo "<h3>🔗 اختبار الروابط</h3>";
echo "<a href='index.php' class='btn'>🏠 الصفحة الرئيسية</a>";
echo "<a href='index.php?route=login' class='btn'>🔐 تسجيل الدخول</a>";

echo "<h3>🔑 بيانات الدخول</h3>";
echo "<p><strong>اسم المستخدم:</strong> admin</p>";
echo "<p><strong>كلمة المرور:</strong> password</p>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
