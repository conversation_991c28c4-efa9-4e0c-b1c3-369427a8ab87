<?php
/**
 * نموذج العميل
 * Customer Model
 */

require_once 'BaseModel.php';

class Customer extends BaseModel
{
    protected $table = 'customers';
    protected $fillable = [
        'first_name', 'last_name', 'phone', 'email', 'national_id', 'passport_number',
        'date_of_birth', 'nationality_country_id', 'address', 'city', 'country_id',
        'created_by_user_id', 'is_active'
    ];
    
    /**
     * البحث عن عميل برقم الهاتف
     */
    public function findByPhone($phone)
    {
        return $this->findBy('phone', $phone);
    }
    
    /**
     * البحث عن عميل بالهوية الوطنية
     */
    public function findByNationalId($nationalId)
    {
        return $this->findBy('national_id', $nationalId);
    }
    
    /**
     * البحث عن عميل برقم الجواز
     */
    public function findByPassport($passportNumber)
    {
        return $this->findBy('passport_number', $passportNumber);
    }
    
    /**
     * الحصول على عميل مع بيانات الدولة
     */
    public function findWithDetails($id)
    {
        $sql = "SELECT c.*, 
                       nc.name as nationality_country_name, nc.name_ar as nationality_country_name_ar,
                       rc.name as residence_country_name, rc.name_ar as residence_country_name_ar,
                       u.first_name as created_by_first_name, u.last_name as created_by_last_name
                FROM customers c 
                LEFT JOIN countries nc ON c.nationality_country_id = nc.id 
                LEFT JOIN countries rc ON c.country_id = rc.id 
                LEFT JOIN users u ON c.created_by_user_id = u.id
                WHERE c.id = ?";
        
        return $this->db->fetch($sql, [$id]);
    }
    
    /**
     * الحصول على جميع العملاء مع بيانات الدول
     */
    public function getAllWithDetails($conditions = [])
    {
        $sql = "SELECT c.*, 
                       nc.name as nationality_country_name, nc.name_ar as nationality_country_name_ar,
                       rc.name as residence_country_name, rc.name_ar as residence_country_name_ar,
                       u.first_name as created_by_first_name, u.last_name as created_by_last_name
                FROM customers c 
                LEFT JOIN countries nc ON c.nationality_country_id = nc.id 
                LEFT JOIN countries rc ON c.country_id = rc.id 
                LEFT JOIN users u ON c.created_by_user_id = u.id";
        
        $params = [];
        if (!empty($conditions)) {
            $whereClause = [];
            foreach ($conditions as $column => $value) {
                $whereClause[] = "c.{$column} = ?";
                $params[] = $value;
            }
            $sql .= " WHERE " . implode(' AND ', $whereClause);
        }
        
        $sql .= " ORDER BY c.created_at DESC";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * البحث في العملاء
     */
    public function search($query, $limit = 20)
    {
        $sql = "SELECT c.*, 
                       nc.name as nationality_country_name,
                       rc.name as residence_country_name
                FROM customers c 
                LEFT JOIN countries nc ON c.nationality_country_id = nc.id 
                LEFT JOIN countries rc ON c.country_id = rc.id 
                WHERE (c.first_name LIKE ? OR c.last_name LIKE ? OR c.phone LIKE ? 
                       OR c.email LIKE ? OR c.national_id LIKE ? OR c.passport_number LIKE ?)
                AND c.is_active = 1
                ORDER BY c.first_name, c.last_name
                LIMIT ?";
        
        $searchTerm = "%{$query}%";
        return $this->db->fetchAll($sql, [
            $searchTerm, $searchTerm, $searchTerm, 
            $searchTerm, $searchTerm, $searchTerm, $limit
        ]);
    }
    
    /**
     * الحصول على العملاء حسب الدولة
     */
    public function getByCountry($countryId)
    {
        return $this->where(['country_id' => $countryId, 'is_active' => 1], 'first_name, last_name');
    }
    
    /**
     * الحصول على العملاء حسب الجنسية
     */
    public function getByNationality($countryId)
    {
        return $this->where(['nationality_country_id' => $countryId, 'is_active' => 1], 'first_name, last_name');
    }
    
    /**
     * التحقق من وجود رقم هاتف
     */
    public function phoneExists($phone, $excludeId = null)
    {
        $sql = "SELECT COUNT(*) as count FROM customers WHERE phone = ?";
        $params = [$phone];
        
        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        $result = $this->db->fetch($sql, $params);
        return $result['count'] > 0;
    }
    
    /**
     * التحقق من وجود الهوية الوطنية
     */
    public function nationalIdExists($nationalId, $excludeId = null)
    {
        if (empty($nationalId)) return false;
        
        $sql = "SELECT COUNT(*) as count FROM customers WHERE national_id = ?";
        $params = [$nationalId];
        
        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        $result = $this->db->fetch($sql, $params);
        return $result['count'] > 0;
    }
    
    /**
     * التحقق من وجود رقم الجواز
     */
    public function passportExists($passportNumber, $excludeId = null)
    {
        if (empty($passportNumber)) return false;
        
        $sql = "SELECT COUNT(*) as count FROM customers WHERE passport_number = ?";
        $params = [$passportNumber];
        
        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        $result = $this->db->fetch($sql, $params);
        return $result['count'] > 0;
    }
    
    /**
     * الحصول على تحويلات العميل
     */
    public function getTransfers($customerId, $limit = 10)
    {
        $sql = "SELECT t.*, 
                       sc.name as sending_currency_name,
                       rc.name as receiving_currency_name,
                       bc.name as beneficiary_country_name
                FROM transfers t
                LEFT JOIN currencies sc ON t.sending_currency_code = sc.code
                LEFT JOIN currencies rc ON t.receiving_currency_code = rc.code
                LEFT JOIN countries bc ON t.beneficiary_country_id = bc.id
                WHERE t.sender_customer_id = ?
                ORDER BY t.created_at DESC
                LIMIT ?";
        
        return $this->db->fetchAll($sql, [$customerId, $limit]);
    }
    
    /**
     * الحصول على إحصائيات العميل
     */
    public function getCustomerStats($customerId)
    {
        $sql = "SELECT 
                    COUNT(*) as total_transfers,
                    SUM(CASE WHEN status = 'paid' THEN 1 ELSE 0 END) as completed_transfers,
                    SUM(CASE WHEN status IN ('pending', 'approved', 'sent', 'received') THEN 1 ELSE 0 END) as pending_transfers,
                    SUM(CASE WHEN status = 'paid' THEN total_paid_by_sender ELSE 0 END) as total_amount_sent,
                    MAX(created_at) as last_transfer_date
                FROM transfers 
                WHERE sender_customer_id = ?";
        
        return $this->db->fetch($sql, [$customerId]);
    }
    
    /**
     * الحصول على وثائق العميل
     */
    public function getDocuments($customerId)
    {
        $sql = "SELECT cd.*, u.first_name as uploaded_by_first_name, u.last_name as uploaded_by_last_name
                FROM customer_documents cd
                LEFT JOIN users u ON cd.uploaded_by_user_id = u.id
                WHERE cd.customer_id = ?
                ORDER BY cd.created_at DESC";
        
        return $this->db->fetchAll($sql, [$customerId]);
    }
    
    /**
     * إضافة وثيقة للعميل
     */
    public function addDocument($customerId, $documentData)
    {
        $documentData['customer_id'] = $customerId;
        $documentData['created_at'] = date('Y-m-d H:i:s');
        
        return $this->db->insert('customer_documents', $documentData);
    }
    
    /**
     * الحصول على إحصائيات العملاء
     */
    public function getStats()
    {
        $sql = "SELECT 
                    COUNT(*) as total_customers,
                    SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_customers,
                    SUM(CASE WHEN is_active = 0 THEN 1 ELSE 0 END) as inactive_customers,
                    SUM(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as new_customers_last_month
                FROM customers";
        
        return $this->db->fetch($sql);
    }
    
    /**
     * الحصول على أفضل العملاء (حسب عدد التحويلات)
     */
    public function getTopCustomers($limit = 10)
    {
        $sql = "SELECT c.*, COUNT(t.id) as transfer_count, SUM(t.total_paid_by_sender) as total_amount
                FROM customers c
                LEFT JOIN transfers t ON c.id = t.sender_customer_id AND t.status = 'paid'
                WHERE c.is_active = 1
                GROUP BY c.id
                HAVING transfer_count > 0
                ORDER BY transfer_count DESC, total_amount DESC
                LIMIT ?";
        
        return $this->db->fetchAll($sql, [$limit]);
    }
}
