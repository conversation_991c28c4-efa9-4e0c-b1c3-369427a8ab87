<?php
/**
 * وحدة تحكم الصفحات العامة
 * Public Controller
 */

require_once 'BaseController.php';

class PublicController extends BaseController
{
    /**
     * الصفحة الرئيسية (الهبوط)
     */
    public function landing()
    {
        // إذا كان المستخدم مسجل دخول، إعادة توجيه للوحة التحكم
        if (AuthHelper::isLoggedIn()) {
            $this->redirect('/dashboard');
        }
        
        // التحقق من remember token
        AuthHelper::checkRememberToken();
        if (AuthHelper::isLoggedIn()) {
            $this->redirect('/dashboard');
        }
        
        $this->view('public/landing', [
            'title' => 'Trust Plus - نظام إدارة التحويلات المالية',
            'flash' => $this->getFlashMessage()
        ]);
    }
}
