<?php
/**
 * اختبار تسجيل الدخول
 */

// تحديد مسار الجذر
define('ROOT_PATH', __DIR__);
define('APP_PATH', ROOT_PATH . '/app');

// بدء الجلسة
session_start();

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>اختبار تسجيل الدخول - Trust Plus</title>";
echo "<style>";
echo "body { font-family: Arial; margin: 40px; background: #f5f5f5; }";
echo ".container { max-width: 500px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; }";
echo ".form-group { margin-bottom: 15px; }";
echo "label { display: block; margin-bottom: 5px; font-weight: bold; }";
echo "input { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }";
echo ".btn { background: #007bff; color: white; padding: 12px 20px; border: none; border-radius: 5px; cursor: pointer; width: 100%; }";
echo ".btn:hover { background: #0056b3; }";
echo ".success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }";
echo ".error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }";
echo "</style>";
echo "</head>";
echo "<body>";
echo "<div class='container'>";

echo "<h1>🔐 اختبار تسجيل الدخول</h1>";

// معالجة تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    
    echo "<h3>📝 البيانات المرسلة:</h3>";
    echo "<p><strong>اسم المستخدم:</strong> " . htmlspecialchars($username) . "</p>";
    echo "<p><strong>كلمة المرور:</strong> " . str_repeat('*', strlen($password)) . "</p>";
    
    // اختبار قاعدة البيانات
    try {
        $config = require 'app/Config/database.php';
        $dsn = "mysql:host={$config['host']};dbname={$config['dbname']};charset={$config['charset']}";
        $pdo = new PDO($dsn, $config['username'], $config['password'], $config['options']);
        
        // البحث عن المستخدم
        $stmt = $pdo->prepare("SELECT * FROM users WHERE username = ? OR email = ?");
        $stmt->execute([$username, $username]);
        $user = $stmt->fetch();
        
        if ($user) {
            echo "<div class='success'>✅ المستخدم موجود في قاعدة البيانات</div>";
            echo "<p><strong>معرف المستخدم:</strong> " . $user['id'] . "</p>";
            echo "<p><strong>الاسم:</strong> " . $user['first_name'] . " " . $user['last_name'] . "</p>";
            echo "<p><strong>البريد الإلكتروني:</strong> " . $user['email'] . "</p>";
            echo "<p><strong>الحالة:</strong> " . ($user['is_active'] ? 'مفعل' : 'غير مفعل') . "</p>";
            
            // التحقق من كلمة المرور
            if (password_verify($password, $user['password_hash'])) {
                echo "<div class='success'>✅ كلمة المرور صحيحة</div>";
                echo "<p><a href='index.php?route=dashboard' style='color: #007bff;'>🏠 الانتقال إلى لوحة التحكم</a></p>";
            } else {
                echo "<div class='error'>❌ كلمة المرور غير صحيحة</div>";
            }
        } else {
            echo "<div class='error'>❌ المستخدم غير موجود</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</div>";
    }
    
    echo "<hr>";
}

// نموذج تسجيل الدخول
echo "<form method='POST'>";
echo "<div class='form-group'>";
echo "<label for='username'>اسم المستخدم:</label>";
echo "<input type='text' id='username' name='username' value='admin' required>";
echo "</div>";

echo "<div class='form-group'>";
echo "<label for='password'>كلمة المرور:</label>";
echo "<input type='password' id='password' name='password' value='password' required>";
echo "</div>";

echo "<button type='submit' class='btn'>🔐 تسجيل الدخول</button>";
echo "</form>";

echo "<hr>";
echo "<h3>🔗 روابط مفيدة:</h3>";
echo "<p><a href='index.php' style='color: #007bff;'>🏠 الصفحة الرئيسية</a></p>";
echo "<p><a href='index.php?route=login' style='color: #007bff;'>🔐 صفحة تسجيل الدخول الرسمية</a></p>";
echo "<p><a href='quick-test.php' style='color: #007bff;'>🧪 الاختبار السريع</a></p>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
