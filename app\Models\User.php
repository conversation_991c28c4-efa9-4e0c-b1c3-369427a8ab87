<?php
/**
 * نموذج المستخدم
 * User Model
 */

require_once 'BaseModel.php';

class User extends BaseModel
{
    protected $table = 'users';
    protected $fillable = [
        'username', 'email', 'password_hash', 'first_name', 'last_name', 
        'phone', 'role_id', 'branch_id', 'is_active', 'last_login', 'remember_token'
    ];
    
    /**
     * البحث عن مستخدم بالبريد الإلكتروني
     */
    public function findByEmail($email)
    {
        return $this->findBy('email', $email);
    }
    
    /**
     * البحث عن مستخدم باسم المستخدم
     */
    public function findByUsername($username)
    {
        return $this->findBy('username', $username);
    }
    
    /**
     * البحث عن مستخدم بـ remember token
     */
    public function findByRememberToken($token)
    {
        return $this->findBy('remember_token', $token);
    }
    
    /**
     * الحصول على مستخدم مع بيانات الدور والفرع
     */
    public function findWithDetails($id)
    {
        $sql = "SELECT u.*, r.name as role_name, r.name_ar as role_name_ar, 
                       r.permissions, b.name as branch_name, b.code as branch_code
                FROM users u 
                LEFT JOIN roles r ON u.role_id = r.id 
                LEFT JOIN branches b ON u.branch_id = b.id 
                WHERE u.id = ?";
        
        return $this->db->fetch($sql, [$id]);
    }
    
    /**
     * الحصول على جميع المستخدمين مع بيانات الأدوار والفروع
     */
    public function getAllWithDetails($conditions = [])
    {
        $sql = "SELECT u.*, r.name as role_name, r.name_ar as role_name_ar, 
                       b.name as branch_name, b.code as branch_code
                FROM users u 
                LEFT JOIN roles r ON u.role_id = r.id 
                LEFT JOIN branches b ON u.branch_id = b.id";
        
        $params = [];
        if (!empty($conditions)) {
            $whereClause = [];
            foreach ($conditions as $column => $value) {
                $whereClause[] = "u.{$column} = ?";
                $params[] = $value;
            }
            $sql .= " WHERE " . implode(' AND ', $whereClause);
        }
        
        $sql .= " ORDER BY u.created_at DESC";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * إنشاء مستخدم جديد
     */
    public function createUser($data)
    {
        // تشفير كلمة المرور
        if (isset($data['password'])) {
            $data['password_hash'] = AuthHelper::hashPassword($data['password']);
            unset($data['password']);
        }
        
        return $this->create($data);
    }
    
    /**
     * تحديث كلمة المرور
     */
    public function updatePassword($userId, $newPassword)
    {
        $hashedPassword = AuthHelper::hashPassword($newPassword);
        return $this->update($userId, ['password_hash' => $hashedPassword]);
    }
    
    /**
     * تحديث آخر تسجيل دخول
     */
    public function updateLastLogin($userId)
    {
        return $this->update($userId, ['last_login' => date('Y-m-d H:i:s')]);
    }
    
    /**
     * تحديث remember token
     */
    public function updateRememberToken($userId, $token)
    {
        return $this->update($userId, ['remember_token' => $token]);
    }
    
    /**
     * الحصول على المستخدمين حسب الفرع
     */
    public function getByBranch($branchId)
    {
        return $this->where(['branch_id' => $branchId, 'is_active' => 1], 'first_name, last_name');
    }
    
    /**
     * الحصول على المستخدمين حسب الدور
     */
    public function getByRole($roleId)
    {
        return $this->where(['role_id' => $roleId, 'is_active' => 1], 'first_name, last_name');
    }
    
    /**
     * البحث في المستخدمين
     */
    public function search($query, $limit = 20)
    {
        $sql = "SELECT u.*, r.name as role_name, b.name as branch_name
                FROM users u 
                LEFT JOIN roles r ON u.role_id = r.id 
                LEFT JOIN branches b ON u.branch_id = b.id 
                WHERE (u.first_name LIKE ? OR u.last_name LIKE ? OR u.username LIKE ? OR u.email LIKE ?)
                AND u.is_active = 1
                ORDER BY u.first_name, u.last_name
                LIMIT ?";
        
        $searchTerm = "%{$query}%";
        return $this->db->fetchAll($sql, [$searchTerm, $searchTerm, $searchTerm, $searchTerm, $limit]);
    }
    
    /**
     * التحقق من وجود بريد إلكتروني
     */
    public function emailExists($email, $excludeId = null)
    {
        $sql = "SELECT COUNT(*) as count FROM users WHERE email = ?";
        $params = [$email];
        
        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        $result = $this->db->fetch($sql, $params);
        return $result['count'] > 0;
    }
    
    /**
     * التحقق من وجود اسم مستخدم
     */
    public function usernameExists($username, $excludeId = null)
    {
        $sql = "SELECT COUNT(*) as count FROM users WHERE username = ?";
        $params = [$username];
        
        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        $result = $this->db->fetch($sql, $params);
        return $result['count'] > 0;
    }
    
    /**
     * تفعيل/إلغاء تفعيل المستخدم
     */
    public function toggleActive($userId)
    {
        $user = $this->find($userId);
        if ($user) {
            $newStatus = $user['is_active'] ? 0 : 1;
            return $this->update($userId, ['is_active' => $newStatus]);
        }
        return false;
    }
    
    /**
     * الحصول على إحصائيات المستخدمين
     */
    public function getStats()
    {
        $sql = "SELECT 
                    COUNT(*) as total_users,
                    SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_users,
                    SUM(CASE WHEN is_active = 0 THEN 1 ELSE 0 END) as inactive_users,
                    SUM(CASE WHEN last_login >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as active_last_month
                FROM users";
        
        return $this->db->fetch($sql);
    }
}
