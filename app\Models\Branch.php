<?php
/**
 * نموذج الفرع
 * Branch Model
 */

require_once 'BaseModel.php';

class Branch extends BaseModel
{
    protected $table = 'branches';
    protected $fillable = [
        'name', 'code', 'address', 'city', 'country_id', 'phone', 'email', 'manager_name', 'is_active'
    ];
    
    /**
     * البحث عن فرع بالكود
     */
    public function findByCode($code)
    {
        return $this->findBy('code', $code);
    }
    
    /**
     * الحصول على الفروع النشطة
     */
    public function getActiveBranches()
    {
        return $this->where(['is_active' => 1], 'name');
    }
    
    /**
     * الحصول على فرع مع بيانات الدولة
     */
    public function findWithDetails($id)
    {
        $sql = "SELECT b.*, c.name as country_name, c.name_ar as country_name_ar
                FROM branches b
                LEFT JOIN countries c ON b.country_id = c.id
                WHERE b.id = ?";
        
        return $this->db->fetch($sql, [$id]);
    }
    
    /**
     * الحصول على جميع الفروع مع بيانات الدول
     */
    public function getAllWithDetails()
    {
        $sql = "SELECT b.*, c.name as country_name, c.name_ar as country_name_ar
                FROM branches b
                LEFT JOIN countries c ON b.country_id = c.id
                WHERE b.is_active = 1
                ORDER BY b.name";
        
        return $this->db->fetchAll($sql);
    }
    
    /**
     * البحث في الفروع
     */
    public function search($query)
    {
        $sql = "SELECT b.*, c.name as country_name
                FROM branches b
                LEFT JOIN countries c ON b.country_id = c.id
                WHERE (b.name LIKE ? OR b.code LIKE ? OR b.city LIKE ? OR b.manager_name LIKE ?)
                AND b.is_active = 1
                ORDER BY b.name
                LIMIT 20";
        
        $searchTerm = "%{$query}%";
        return $this->db->fetchAll($sql, [$searchTerm, $searchTerm, $searchTerm, $searchTerm]);
    }
    
    /**
     * الحصول على الفروع حسب الدولة
     */
    public function getByCountry($countryId)
    {
        return $this->where(['country_id' => $countryId, 'is_active' => 1], 'name');
    }
    
    /**
     * الحصول على مستخدمي الفرع
     */
    public function getUsers($branchId)
    {
        $sql = "SELECT u.*, r.name as role_name, r.name_ar as role_name_ar
                FROM users u
                LEFT JOIN roles r ON u.role_id = r.id
                WHERE u.branch_id = ? AND u.is_active = 1
                ORDER BY u.first_name, u.last_name";
        
        return $this->db->fetchAll($sql, [$branchId]);
    }
    
    /**
     * الحصول على تحويلات الفرع
     */
    public function getTransfers($branchId, $type = null, $limit = 20)
    {
        $sql = "SELECT t.*, 
                       c.first_name as sender_first_name, c.last_name as sender_last_name,
                       sc.symbol as sending_currency_symbol,
                       rc.symbol as receiving_currency_symbol
                FROM transfers t
                LEFT JOIN customers c ON t.sender_customer_id = c.id
                LEFT JOIN currencies sc ON t.sending_currency_code = sc.code
                LEFT JOIN currencies rc ON t.receiving_currency_code = rc.code
                WHERE (t.sending_branch_id = ? OR t.receiving_branch_id = ?)";
        
        $params = [$branchId, $branchId];
        
        if ($type) {
            $sql .= " AND t.transfer_type = ?";
            $params[] = $type;
        }
        
        $sql .= " ORDER BY t.created_at DESC LIMIT ?";
        $params[] = $limit;
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * الحصول على إحصائيات الفرع
     */
    public function getBranchStats($branchId, $dateFrom = null, $dateTo = null)
    {
        $sql = "SELECT 
                    COUNT(*) as total_transfers,
                    SUM(CASE WHEN status = 'paid' THEN 1 ELSE 0 END) as completed_transfers,
                    SUM(CASE WHEN status IN ('pending', 'approved', 'sent', 'received') THEN 1 ELSE 0 END) as pending_transfers,
                    SUM(CASE WHEN status = 'paid' THEN total_paid_by_sender ELSE 0 END) as total_amount,
                    SUM(CASE WHEN status = 'paid' THEN fee_amount ELSE 0 END) as total_fees,
                    SUM(CASE WHEN transfer_type = 'outgoing' THEN 1 ELSE 0 END) as outgoing_count,
                    SUM(CASE WHEN transfer_type = 'incoming' THEN 1 ELSE 0 END) as incoming_count
                FROM transfers 
                WHERE (sending_branch_id = ? OR receiving_branch_id = ?)";
        
        $params = [$branchId, $branchId];
        
        if ($dateFrom) {
            $sql .= " AND DATE(created_at) >= ?";
            $params[] = $dateFrom;
        }
        
        if ($dateTo) {
            $sql .= " AND DATE(created_at) <= ?";
            $params[] = $dateTo;
        }
        
        return $this->db->fetch($sql, $params);
    }
    
    /**
     * التحقق من وجود كود فرع
     */
    public function codeExists($code, $excludeId = null)
    {
        $sql = "SELECT COUNT(*) as count FROM branches WHERE code = ?";
        $params = [$code];
        
        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        $result = $this->db->fetch($sql, $params);
        return $result['count'] > 0;
    }
    
    /**
     * الحصول على إحصائيات عامة للفروع
     */
    public function getStats()
    {
        $sql = "SELECT 
                    COUNT(*) as total_branches,
                    SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_branches,
                    (SELECT COUNT(*) FROM users WHERE branch_id IS NOT NULL AND is_active = 1) as total_users
                FROM branches";
        
        return $this->db->fetch($sql);
    }
    
    /**
     * الحصول على أداء الفروع
     */
    public function getBranchesPerformance($dateFrom = null, $dateTo = null)
    {
        $sql = "SELECT b.*, 
                    COUNT(t.id) as transfer_count,
                    SUM(CASE WHEN t.status = 'paid' THEN t.total_paid_by_sender ELSE 0 END) as total_amount,
                    SUM(CASE WHEN t.status = 'paid' THEN t.fee_amount ELSE 0 END) as total_fees
                FROM branches b
                LEFT JOIN transfers t ON (b.id = t.sending_branch_id OR b.id = t.receiving_branch_id)";
        
        $params = [];
        $whereConditions = ['b.is_active = 1'];
        
        if ($dateFrom) {
            $whereConditions[] = "(t.created_at IS NULL OR DATE(t.created_at) >= ?)";
            $params[] = $dateFrom;
        }
        
        if ($dateTo) {
            $whereConditions[] = "(t.created_at IS NULL OR DATE(t.created_at) <= ?)";
            $params[] = $dateTo;
        }
        
        if (!empty($whereConditions)) {
            $sql .= " WHERE " . implode(' AND ', $whereConditions);
        }
        
        $sql .= " GROUP BY b.id ORDER BY total_amount DESC";
        
        return $this->db->fetchAll($sql, $params);
    }
}
