<?php
/**
 * وحدة التحكم الأساسية
 * Base Controller Class
 */

abstract class BaseController
{
    protected $request;
    protected $viewData = [];
    
    public function __construct()
    {
        $this->request = new Request();
        $this->loadModels();
    }
    
    /**
     * تحميل النماذج المطلوبة
     */
    protected function loadModels()
    {
        // تحميل النماذج الأساسية
        require_once APP_PATH . '/Models/BaseModel.php';
        require_once APP_PATH . '/Models/User.php';
        require_once APP_PATH . '/Models/Customer.php';
        require_once APP_PATH . '/Models/Transfer.php';
        require_once APP_PATH . '/Models/Currency.php';
        require_once APP_PATH . '/Models/Country.php';
        require_once APP_PATH . '/Models/Branch.php';
        require_once APP_PATH . '/Models/Role.php';
        require_once APP_PATH . '/Models/Account.php';
        require_once APP_PATH . '/Models/JournalEntry.php';
    }
    
    /**
     * عرض صفحة
     */
    protected function view($viewPath, $data = [])
    {
        // دمج البيانات
        $data = array_merge($this->viewData, $data);
        
        // استخراج المتغيرات
        extract($data);
        
        // تحديد مسار الملف
        $viewFile = APP_PATH . '/Views/' . $viewPath . '.php';
        
        if (!file_exists($viewFile)) {
            throw new Exception("View file not found: {$viewFile}");
        }
        
        // تضمين الملف
        include $viewFile;
    }
    
    /**
     * إعادة توجيه
     */
    protected function redirect($url, $message = null, $type = 'info')
    {
        if ($message) {
            $_SESSION['flash_message'] = $message;
            $_SESSION['flash_type'] = $type;
        }
        
        // إضافة المسار الأساسي إذا لم يكن موجود
        if (strpos($url, 'http') !== 0 && strpos($url, '/Trust_Plus') !== 0) {
            $url = '/Trust_Plus' . $url;
        }
        
        header("Location: {$url}");
        exit;
    }
    
    /**
     * إرجاع JSON
     */
    protected function json($data, $statusCode = 200)
    {
        http_response_code($statusCode);
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    /**
     * التحقق من صحة البيانات
     */
    protected function validate($rules)
    {
        $errors = $this->request->validate($rules);
        
        if (!empty($errors)) {
            if ($this->request->isAjax()) {
                $this->json(['success' => false, 'errors' => $errors], 422);
            } else {
                $_SESSION['validation_errors'] = $errors;
                $_SESSION['old_input'] = $this->request->all();
                $this->redirect($_SERVER['HTTP_REFERER'] ?? '/dashboard', 'يرجى تصحيح الأخطاء المدخلة', 'error');
            }
        }
        
        return true;
    }
    
    /**
     * الحصول على رسالة فلاش
     */
    protected function getFlashMessage()
    {
        $message = $_SESSION['flash_message'] ?? null;
        $type = $_SESSION['flash_type'] ?? 'info';
        
        unset($_SESSION['flash_message'], $_SESSION['flash_type']);
        
        return $message ? ['message' => $message, 'type' => $type] : null;
    }
    
    /**
     * الحصول على أخطاء التحقق
     */
    protected function getValidationErrors()
    {
        $errors = $_SESSION['validation_errors'] ?? [];
        unset($_SESSION['validation_errors']);
        return $errors;
    }
    
    /**
     * الحصول على البيانات القديمة
     */
    protected function getOldInput($key = null, $default = null)
    {
        $oldInput = $_SESSION['old_input'] ?? [];
        
        if ($key === null) {
            unset($_SESSION['old_input']);
            return $oldInput;
        }
        
        return $oldInput[$key] ?? $default;
    }
    
    /**
     * تعيين بيانات العرض
     */
    protected function setViewData($key, $value = null)
    {
        if (is_array($key)) {
            $this->viewData = array_merge($this->viewData, $key);
        } else {
            $this->viewData[$key] = $value;
        }
    }
    
    /**
     * التحقق من الصلاحية
     */
    protected function requirePermission($permission)
    {
        AuthHelper::requirePermission($permission);
    }
    
    /**
     * التحقق من تسجيل الدخول
     */
    protected function requireLogin()
    {
        AuthHelper::requireLogin();
    }
    
    /**
     * الحصول على المستخدم الحالي
     */
    protected function getCurrentUser()
    {
        return AuthHelper::getCurrentUser();
    }
    
    /**
     * معالجة رفع الملفات
     */
    protected function handleFileUpload($fileKey, $uploadPath = 'uploads/', $allowedTypes = null)
    {
        if (!$this->request->hasFile($fileKey)) {
            return null;
        }
        
        $file = $this->request->file($fileKey);
        
        // التحقق من وجود أخطاء
        if ($file['error'] !== UPLOAD_ERR_OK) {
            throw new Exception('خطأ في رفع الملف');
        }
        
        // التحقق من نوع الملف
        $allowedTypes = $allowedTypes ?? ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx'];
        $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        
        if (!in_array($fileExtension, $allowedTypes)) {
            throw new Exception('نوع الملف غير مسموح');
        }
        
        // التحقق من حجم الملف
        $maxSize = 5 * 1024 * 1024; // 5MB
        if ($file['size'] > $maxSize) {
            throw new Exception('حجم الملف كبير جداً');
        }
        
        // إنشاء اسم ملف فريد
        $fileName = uniqid() . '_' . time() . '.' . $fileExtension;
        $fullUploadPath = STORAGE_PATH . '/' . $uploadPath;
        
        // إنشاء المجلد إذا لم يكن موجود
        if (!is_dir($fullUploadPath)) {
            mkdir($fullUploadPath, 0755, true);
        }
        
        $filePath = $fullUploadPath . $fileName;
        
        // نقل الملف
        if (!move_uploaded_file($file['tmp_name'], $filePath)) {
            throw new Exception('فشل في حفظ الملف');
        }
        
        return $uploadPath . $fileName;
    }
    
    /**
     * تسجيل الأنشطة
     */
    protected function logActivity($action, $description, $relatedId = null, $relatedType = null)
    {
        $user = $this->getCurrentUser();
        
        if (!$user) {
            return;
        }
        
        $db = Database::getInstance();
        
        $db->insert('activity_logs', [
            'user_id' => $user['id'],
            'action' => $action,
            'description' => $description,
            'related_id' => $relatedId,
            'related_type' => $relatedType,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? null,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null,
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * معالجة الأخطاء
     */
    protected function handleError($e, $defaultMessage = 'حدث خطأ غير متوقع')
    {
        error_log($e->getMessage());
        
        if ($this->request->isAjax()) {
            $this->json([
                'success' => false,
                'message' => $defaultMessage
            ], 500);
        } else {
            $this->redirect($_SERVER['HTTP_REFERER'] ?? '/dashboard', $defaultMessage, 'error');
        }
    }
}
