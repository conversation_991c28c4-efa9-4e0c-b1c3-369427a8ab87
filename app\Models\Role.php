<?php
/**
 * نموذج الدور
 * Role Model
 */

require_once 'BaseModel.php';

class Role extends BaseModel
{
    protected $table = 'roles';
    protected $fillable = [
        'name', 'name_ar', 'description', 'permissions', 'is_active'
    ];
    
    /**
     * البحث عن دور بالاسم
     */
    public function findByName($name)
    {
        return $this->findBy('name', $name);
    }
    
    /**
     * الحصول على الأدوار النشطة
     */
    public function getActiveRoles()
    {
        return $this->where(['is_active' => 1], 'name');
    }
    
    /**
     * الحصول على مستخدمي الدور
     */
    public function getUsers($roleId)
    {
        $sql = "SELECT u.*, b.name as branch_name
                FROM users u
                LEFT JOIN branches b ON u.branch_id = b.id
                WHERE u.role_id = ? AND u.is_active = 1
                ORDER BY u.first_name, u.last_name";
        
        return $this->db->fetchAll($sql, [$roleId]);
    }
    
    /**
     * التحقق من صلاحية معينة
     */
    public function hasPermission($roleId, $permission)
    {
        $role = $this->find($roleId);
        
        if (!$role) {
            return false;
        }
        
        $permissions = json_decode($role['permissions'], true);
        
        if (!is_array($permissions)) {
            return false;
        }
        
        return in_array($permission, $permissions) || in_array('all', $permissions);
    }
    
    /**
     * إضافة صلاحية للدور
     */
    public function addPermission($roleId, $permission)
    {
        $role = $this->find($roleId);
        
        if (!$role) {
            return false;
        }
        
        $permissions = json_decode($role['permissions'], true);
        
        if (!is_array($permissions)) {
            $permissions = [];
        }
        
        if (!in_array($permission, $permissions)) {
            $permissions[] = $permission;
            return $this->update($roleId, ['permissions' => json_encode($permissions)]);
        }
        
        return true;
    }
    
    /**
     * إزالة صلاحية من الدور
     */
    public function removePermission($roleId, $permission)
    {
        $role = $this->find($roleId);
        
        if (!$role) {
            return false;
        }
        
        $permissions = json_decode($role['permissions'], true);
        
        if (!is_array($permissions)) {
            return true;
        }
        
        $permissions = array_filter($permissions, function($p) use ($permission) {
            return $p !== $permission;
        });
        
        return $this->update($roleId, ['permissions' => json_encode(array_values($permissions))]);
    }
    
    /**
     * تحديث صلاحيات الدور
     */
    public function updatePermissions($roleId, $permissions)
    {
        if (!is_array($permissions)) {
            $permissions = [];
        }
        
        return $this->update($roleId, ['permissions' => json_encode($permissions)]);
    }
    
    /**
     * الحصول على جميع الصلاحيات المتاحة
     */
    public function getAvailablePermissions()
    {
        return [
            'all' => 'جميع الصلاحيات',
            'admin' => 'صلاحيات المدير',
            
            // صلاحيات التحويلات
            'transfers_view' => 'عرض التحويلات',
            'transfers_create' => 'إنشاء تحويل',
            'transfers_edit' => 'تعديل التحويل',
            'transfers_delete' => 'حذف التحويل',
            'transfers_approve' => 'الموافقة على التحويلات',
            'transfers_manage' => 'إدارة التحويلات',
            
            // صلاحيات العملاء
            'customers_view' => 'عرض العملاء',
            'customers_create' => 'إضافة عميل',
            'customers_edit' => 'تعديل العميل',
            'customers_delete' => 'حذف العميل',
            'customers_manage' => 'إدارة العملاء',
            
            // صلاحيات المستخدمين
            'users_view' => 'عرض المستخدمين',
            'users_create' => 'إضافة مستخدم',
            'users_edit' => 'تعديل المستخدم',
            'users_delete' => 'حذف المستخدم',
            'users_manage' => 'إدارة المستخدمين',
            
            // صلاحيات التقارير
            'reports_view' => 'عرض التقارير',
            'reports_export' => 'تصدير التقارير',
            'reports_manage' => 'إدارة التقارير',
            
            // صلاحيات الحسابات
            'accounting_view' => 'عرض الحسابات',
            'accounting_create' => 'إنشاء قيود',
            'accounting_edit' => 'تعديل القيود',
            'accounting_approve' => 'الموافقة على القيود',
            'accounting_manage' => 'إدارة الحسابات',
            
            // صلاحيات الإعدادات
            'settings_view' => 'عرض الإعدادات',
            'settings_edit' => 'تعديل الإعدادات',
            'settings_manage' => 'إدارة الإعدادات',
            
            // صلاحيات الفروع
            'branches_view' => 'عرض الفروع',
            'branches_manage' => 'إدارة الفروع',
            
            // صلاحيات العملات
            'currencies_view' => 'عرض العملات',
            'currencies_manage' => 'إدارة العملات',
            'exchange_rates_manage' => 'إدارة أسعار الصرف'
        ];
    }
    
    /**
     * البحث في الأدوار
     */
    public function search($query)
    {
        $sql = "SELECT * FROM roles 
                WHERE (name LIKE ? OR name_ar LIKE ? OR description LIKE ?)
                AND is_active = 1
                ORDER BY name
                LIMIT 20";
        
        $searchTerm = "%{$query}%";
        return $this->db->fetchAll($sql, [$searchTerm, $searchTerm, $searchTerm]);
    }
    
    /**
     * التحقق من وجود اسم دور
     */
    public function nameExists($name, $excludeId = null)
    {
        $sql = "SELECT COUNT(*) as count FROM roles WHERE name = ?";
        $params = [$name];
        
        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        $result = $this->db->fetch($sql, $params);
        return $result['count'] > 0;
    }
    
    /**
     * الحصول على إحصائيات الأدوار
     */
    public function getStats()
    {
        $sql = "SELECT 
                    COUNT(*) as total_roles,
                    SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_roles,
                    (SELECT COUNT(*) FROM users WHERE role_id IS NOT NULL AND is_active = 1) as total_users_with_roles
                FROM roles";
        
        return $this->db->fetch($sql);
    }
    
    /**
     * الحصول على توزيع المستخدمين على الأدوار
     */
    public function getUserDistribution()
    {
        $sql = "SELECT r.name, r.name_ar, COUNT(u.id) as user_count
                FROM roles r
                LEFT JOIN users u ON r.id = u.role_id AND u.is_active = 1
                WHERE r.is_active = 1
                GROUP BY r.id
                ORDER BY user_count DESC";
        
        return $this->db->fetchAll($sql);
    }
    
    /**
     * إنشاء دور جديد مع صلاحيات افتراضية
     */
    public function createRole($data, $permissions = [])
    {
        $data['permissions'] = json_encode($permissions);
        return $this->create($data);
    }
}
