<?php
/**
 * نموذج الدولة
 * Country Model
 */

require_once 'BaseModel.php';

class Country extends BaseModel
{
    protected $table = 'countries';
    protected $fillable = [
        'name', 'name_ar', 'code', 'phone_code', 'currency_code', 'is_active'
    ];
    
    /**
     * البحث عن دولة بالكود
     */
    public function findByCode($code)
    {
        return $this->findBy('code', $code);
    }
    
    /**
     * الحصول على الدول النشطة
     */
    public function getActiveCountries()
    {
        return $this->where(['is_active' => 1], 'name');
    }
    
    /**
     * الحصول على دولة مع عملتها
     */
    public function findWithCurrency($id)
    {
        $sql = "SELECT c.*, cur.name as currency_name, cur.symbol as currency_symbol
                FROM countries c
                LEFT JOIN currencies cur ON c.currency_code = cur.code
                WHERE c.id = ?";
        
        return $this->db->fetch($sql, [$id]);
    }
    
    /**
     * الحصول على جميع الدول مع عملاتها
     */
    public function getAllWithCurrencies()
    {
        $sql = "SELECT c.*, cur.name as currency_name, cur.symbol as currency_symbol
                FROM countries c
                LEFT JOIN currencies cur ON c.currency_code = cur.code
                WHERE c.is_active = 1
                ORDER BY c.name";
        
        return $this->db->fetchAll($sql);
    }
    
    /**
     * البحث في الدول
     */
    public function search($query)
    {
        $sql = "SELECT * FROM countries 
                WHERE (name LIKE ? OR name_ar LIKE ? OR code LIKE ?)
                AND is_active = 1
                ORDER BY name
                LIMIT 20";
        
        $searchTerm = "%{$query}%";
        return $this->db->fetchAll($sql, [$searchTerm, $searchTerm, $searchTerm]);
    }
    
    /**
     * الحصول على الدول حسب العملة
     */
    public function getByCurrency($currencyCode)
    {
        return $this->where(['currency_code' => $currencyCode, 'is_active' => 1], 'name');
    }
    
    /**
     * التحقق من وجود كود دولة
     */
    public function codeExists($code, $excludeId = null)
    {
        $sql = "SELECT COUNT(*) as count FROM countries WHERE code = ?";
        $params = [$code];
        
        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        $result = $this->db->fetch($sql, $params);
        return $result['count'] > 0;
    }
    
    /**
     * الحصول على إحصائيات الدول
     */
    public function getStats()
    {
        $sql = "SELECT 
                    COUNT(*) as total_countries,
                    SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_countries
                FROM countries";
        
        return $this->db->fetch($sql);
    }
    
    /**
     * الحصول على الدول الأكثر استخداماً في التحويلات
     */
    public function getMostUsedInTransfers($limit = 10)
    {
        $sql = "SELECT c.*, 
                    (SELECT COUNT(*) FROM transfers WHERE beneficiary_country_id = c.id) as transfer_count,
                    (SELECT COUNT(*) FROM customers WHERE country_id = c.id OR nationality_country_id = c.id) as customer_count
                FROM countries c
                WHERE c.is_active = 1
                ORDER BY transfer_count DESC, customer_count DESC
                LIMIT ?";
        
        return $this->db->fetchAll($sql, [$limit]);
    }
    
    /**
     * الحصول على دول الخليج العربي
     */
    public function getGCCCountries()
    {
        $gccCodes = ['SAU', 'ARE', 'KWT', 'BHR', 'QAT', 'OMN'];
        
        $placeholders = str_repeat('?,', count($gccCodes) - 1) . '?';
        $sql = "SELECT * FROM countries WHERE code IN ({$placeholders}) AND is_active = 1 ORDER BY name";
        
        return $this->db->fetchAll($sql, $gccCodes);
    }
    
    /**
     * الحصول على الدول العربية
     */
    public function getArabCountries()
    {
        $arabCodes = ['SAU', 'ARE', 'KWT', 'BHR', 'QAT', 'OMN', 'JOR', 'EGY', 'LBN', 'SYR', 'IRQ', 'YEM', 'MAR', 'TUN', 'DZA', 'LBY', 'SDN', 'SOM', 'DJI', 'COM', 'MRT'];
        
        $placeholders = str_repeat('?,', count($arabCodes) - 1) . '?';
        $sql = "SELECT * FROM countries WHERE code IN ({$placeholders}) AND is_active = 1 ORDER BY name";
        
        return $this->db->fetchAll($sql, $arabCodes);
    }
}
