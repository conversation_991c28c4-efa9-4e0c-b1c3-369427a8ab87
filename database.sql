-- قاعدة بيانات نظام Trust Plus
-- Trust Plus Database Schema

CREATE DATABASE IF NOT EXISTS trust_plus CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE trust_plus;

-- جدول الدول
CREATE TABLE countries (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VA<PERSON>HA<PERSON>(100) NOT NULL,
    name_ar VARCHAR(100) NOT NULL,
    code VARCHAR(3) NOT NULL UNIQUE,
    phone_code VARCHAR(10),
    currency_code VARCHAR(3),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول العملات
CREATE TABLE currencies (
    id INT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(3) NOT NULL UNIQUE,
    name VARCHAR(50) NOT NULL,
    name_ar VARCHAR(50) NOT NULL,
    symbol VARCHAR(10),
    decimal_places INT DEFAULT 2,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول أسعار الصرف
CREATE TABLE exchange_rates (
    id INT PRIMARY KEY AUTO_INCREMENT,
    from_currency VARCHAR(3) NOT NULL,
    to_currency VARCHAR(3) NOT NULL,
    rate DECIMAL(15,6) NOT NULL,
    effective_date DATE NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_by_user_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (from_currency) REFERENCES currencies(code),
    FOREIGN KEY (to_currency) REFERENCES currencies(code),
    UNIQUE KEY unique_rate (from_currency, to_currency, effective_date)
);

-- جدول الفروع
CREATE TABLE branches (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(20) UNIQUE,
    address TEXT,
    city VARCHAR(50),
    country_id INT,
    phone VARCHAR(20),
    email VARCHAR(100),
    manager_name VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (country_id) REFERENCES countries(id)
);

-- جدول الأدوار والصلاحيات
CREATE TABLE roles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL UNIQUE,
    name_ar VARCHAR(50) NOT NULL,
    description TEXT,
    permissions JSON,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول المستخدمين
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    phone VARCHAR(20),
    role_id INT NOT NULL,
    branch_id INT,
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    remember_token VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (role_id) REFERENCES roles(id),
    FOREIGN KEY (branch_id) REFERENCES branches(id)
);

-- جدول العملاء
CREATE TABLE customers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    email VARCHAR(100),
    national_id VARCHAR(50),
    passport_number VARCHAR(50),
    date_of_birth DATE,
    nationality_country_id INT,
    address TEXT,
    city VARCHAR(50),
    country_id INT,
    created_by_user_id INT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (nationality_country_id) REFERENCES countries(id),
    FOREIGN KEY (country_id) REFERENCES countries(id),
    FOREIGN KEY (created_by_user_id) REFERENCES users(id)
);

-- جدول وثائق العملاء
CREATE TABLE customer_documents (
    id INT PRIMARY KEY AUTO_INCREMENT,
    customer_id INT NOT NULL,
    document_type ENUM('national_id', 'passport', 'driving_license', 'other') NOT NULL,
    document_number VARCHAR(50),
    file_path VARCHAR(255),
    uploaded_by_user_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
    FOREIGN KEY (uploaded_by_user_id) REFERENCES users(id)
);

-- جدول التحويلات
CREATE TABLE transfers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    transfer_number VARCHAR(50) NOT NULL UNIQUE,
    sender_customer_id INT NOT NULL,
    beneficiary_name VARCHAR(100) NOT NULL,
    beneficiary_phone VARCHAR(20),
    beneficiary_address TEXT,
    beneficiary_city VARCHAR(50),
    beneficiary_country_id INT,
    beneficiary_bank_name VARCHAR(100),
    beneficiary_account_number VARCHAR(50),
    beneficiary_iban VARCHAR(50),
    sending_amount DECIMAL(15,2) NOT NULL,
    sending_currency_code VARCHAR(3) NOT NULL,
    receiving_amount DECIMAL(15,2) NOT NULL,
    receiving_currency_code VARCHAR(3) NOT NULL,
    exchange_rate DECIMAL(15,6) NOT NULL,
    fee_amount DECIMAL(15,2) NOT NULL,
    fee_currency_code VARCHAR(3) NOT NULL,
    total_paid_by_sender DECIMAL(15,2) NOT NULL,
    purpose VARCHAR(255),
    notes TEXT,
    status ENUM('pending', 'approved', 'sent', 'received', 'paid', 'cancelled', 'rejected') DEFAULT 'pending',
    transfer_type ENUM('outgoing', 'incoming') NOT NULL,
    sending_branch_id INT,
    receiving_branch_id INT,
    created_by_user_id INT NOT NULL,
    approved_by_user_id INT,
    approved_at TIMESTAMP NULL,
    sent_at TIMESTAMP NULL,
    received_at TIMESTAMP NULL,
    paid_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (sender_customer_id) REFERENCES customers(id),
    FOREIGN KEY (beneficiary_country_id) REFERENCES countries(id),
    FOREIGN KEY (sending_currency_code) REFERENCES currencies(code),
    FOREIGN KEY (receiving_currency_code) REFERENCES currencies(code),
    FOREIGN KEY (fee_currency_code) REFERENCES currencies(code),
    FOREIGN KEY (sending_branch_id) REFERENCES branches(id),
    FOREIGN KEY (receiving_branch_id) REFERENCES branches(id),
    FOREIGN KEY (created_by_user_id) REFERENCES users(id),
    FOREIGN KEY (approved_by_user_id) REFERENCES users(id)
);

-- جدول شجرة الحسابات
CREATE TABLE chart_of_accounts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    account_code VARCHAR(20) NOT NULL UNIQUE,
    account_name VARCHAR(255) NOT NULL,
    account_name_ar VARCHAR(255) NOT NULL,
    account_type ENUM('asset', 'liability', 'equity', 'revenue', 'expense') NOT NULL,
    parent_account_id INT,
    normal_balance ENUM('debit', 'credit') NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_account_id) REFERENCES chart_of_accounts(id)
);

-- جدول قيود اليومية (رأس القيد)
CREATE TABLE journal_entries (
    id INT PRIMARY KEY AUTO_INCREMENT,
    entry_number VARCHAR(50) NOT NULL UNIQUE,
    entry_date DATE NOT NULL,
    description TEXT NOT NULL,
    reference_type VARCHAR(50),
    reference_id INT,
    total_amount DECIMAL(15,2) NOT NULL,
    created_by_user_id INT NOT NULL,
    approved_by_user_id INT,
    approved_at TIMESTAMP NULL,
    is_approved BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by_user_id) REFERENCES users(id),
    FOREIGN KEY (approved_by_user_id) REFERENCES users(id)
);

-- جدول تفاصيل قيود اليومية
CREATE TABLE journal_entry_details (
    id INT PRIMARY KEY AUTO_INCREMENT,
    journal_entry_id INT NOT NULL,
    account_id INT NOT NULL,
    debit_amount DECIMAL(15,2) DEFAULT 0,
    credit_amount DECIMAL(15,2) DEFAULT 0,
    currency_code VARCHAR(3) NOT NULL,
    description VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (journal_entry_id) REFERENCES journal_entries(id) ON DELETE CASCADE,
    FOREIGN KEY (account_id) REFERENCES chart_of_accounts(id),
    FOREIGN KEY (currency_code) REFERENCES currencies(code)
);

-- جدول أرصدة الحسابات (للتسريع)
CREATE TABLE account_balances (
    id INT PRIMARY KEY AUTO_INCREMENT,
    account_id INT NOT NULL,
    currency_code VARCHAR(3) NOT NULL,
    debit_balance DECIMAL(15,2) DEFAULT 0,
    credit_balance DECIMAL(15,2) DEFAULT 0,
    net_balance DECIMAL(15,2) DEFAULT 0,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (account_id) REFERENCES chart_of_accounts(id),
    FOREIGN KEY (currency_code) REFERENCES currencies(code),
    UNIQUE KEY unique_account_currency (account_id, currency_code)
);

-- إدراج البيانات الأساسية

-- العملات الأساسية
INSERT INTO currencies (code, name, name_ar, symbol, decimal_places) VALUES
('SAR', 'Saudi Riyal', 'ريال سعودي', 'ر.س', 2),
('USD', 'US Dollar', 'دولار أمريكي', '$', 2),
('EUR', 'Euro', 'يورو', '€', 2),
('GBP', 'British Pound', 'جنيه إسترليني', '£', 2),
('AED', 'UAE Dirham', 'درهم إماراتي', 'د.إ', 2),
('KWD', 'Kuwaiti Dinar', 'دينار كويتي', 'د.ك', 3),
('BHD', 'Bahraini Dinar', 'دينار بحريني', 'د.ب', 3),
('QAR', 'Qatari Riyal', 'ريال قطري', 'ر.ق', 2),
('OMR', 'Omani Rial', 'ريال عماني', 'ر.ع', 3),
('JOD', 'Jordanian Dinar', 'دينار أردني', 'د.أ', 3);

-- الدول الأساسية
INSERT INTO countries (name, name_ar, code, phone_code, currency_code) VALUES
('Saudi Arabia', 'المملكة العربية السعودية', 'SAU', '+966', 'SAR'),
('United States', 'الولايات المتحدة الأمريكية', 'USA', '+1', 'USD'),
('United Kingdom', 'المملكة المتحدة', 'GBR', '+44', 'GBP'),
('United Arab Emirates', 'الإمارات العربية المتحدة', 'ARE', '+971', 'AED'),
('Kuwait', 'الكويت', 'KWT', '+965', 'KWD'),
('Bahrain', 'البحرين', 'BHR', '+973', 'BHD'),
('Qatar', 'قطر', 'QAT', '+974', 'QAR'),
('Oman', 'عمان', 'OMN', '+968', 'OMR'),
('Jordan', 'الأردن', 'JOR', '+962', 'JOD'),
('Egypt', 'مصر', 'EGY', '+20', 'EGP');

-- الأدوار الأساسية
INSERT INTO roles (name, name_ar, description, permissions) VALUES
('admin', 'مدير النظام', 'صلاحيات كاملة على النظام', '["all"]'),
('manager', 'مدير', 'إدارة الفرع والموافقة على التحويلات', '["transfers_approve", "transfers_view", "customers_manage", "reports_view", "users_view"]'),
('cashier', 'أمين صندوق', 'إنشاء التحويلات وإدارة العملاء', '["transfers_create", "transfers_view", "customers_manage"]'),
('accountant', 'محاسب', 'إدارة الحسابات والتقارير المالية', '["accounting_manage", "reports_view", "transfers_view"]'),
('viewer', 'مستعلم', 'عرض البيانات فقط', '["transfers_view", "customers_view", "reports_view"]');

-- الفرع الرئيسي
INSERT INTO branches (name, code, address, city, country_id, phone, email, manager_name) VALUES
('الفرع الرئيسي', 'MAIN', 'شارع الملك فهد، الرياض', 'الرياض', 1, '+************', '<EMAIL>', 'أحمد محمد');

-- المستخدم الافتراضي (admin)
INSERT INTO users (username, email, password_hash, first_name, last_name, phone, role_id, branch_id) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير', 'النظام', '+************', 1, 1);
-- كلمة المرور: password

-- شجرة الحسابات الأساسية
-- الأصول (Assets)
INSERT INTO chart_of_accounts (account_code, account_name, account_name_ar, account_type, parent_account_id, normal_balance) VALUES
('1000', 'Assets', 'الأصول', 'asset', NULL, 'debit'),
('1100', 'Current Assets', 'الأصول المتداولة', 'asset', 1, 'debit'),
('1110', 'Cash and Cash Equivalents', 'النقدية وما في حكمها', 'asset', 2, 'debit'),
('1111', 'Cash in Hand - Main Branch', 'النقدية بالصندوق - الفرع الرئيسي', 'asset', 3, 'debit'),
('1112', 'Bank Account - Main', 'حساب بنكي رئيسي', 'asset', 3, 'debit'),
('1120', 'Accounts Receivable', 'المدينون', 'asset', 2, 'debit'),
('1121', 'Customer Receivables', 'مستحقات من العملاء', 'asset', 6, 'debit'),
('1130', 'Due from Branches/Agents', 'مستحقات من الفروع والوكلاء', 'asset', 2, 'debit'),
('1131', 'Due from Branch 1', 'مستحقات من الفرع 1', 'asset', 8, 'debit'),

-- الخصوم (Liabilities)
('2000', 'Liabilities', 'الخصوم', 'liability', NULL, 'credit'),
('2100', 'Current Liabilities', 'الخصوم المتداولة', 'liability', 10, 'credit'),
('2110', 'Accounts Payable', 'الدائنون', 'liability', 11, 'credit'),
('2111', 'Supplier Payables', 'مستحقات للموردين', 'liability', 12, 'credit'),
('2120', 'Customer Funds Pending', 'أموال العملاء المعلقة', 'liability', 11, 'credit'),
('2121', 'Outgoing Transfer Funds', 'أموال التحويلات الصادرة', 'liability', 14, 'credit'),
('2122', 'Incoming Transfer Funds', 'أموال التحويلات الواردة', 'liability', 14, 'credit'),
('2130', 'Due to Branches/Agents', 'مستحقات للفروع والوكلاء', 'liability', 11, 'credit'),
('2131', 'Due to Branch 1', 'مستحقات للفرع 1', 'liability', 17, 'credit'),

-- حقوق الملكية (Equity)
('3000', 'Equity', 'حقوق الملكية', 'equity', NULL, 'credit'),
('3100', 'Capital', 'رأس المال', 'equity', 19, 'credit'),
('3110', 'Paid-in Capital', 'رأس المال المدفوع', 'equity', 20, 'credit'),
('3200', 'Retained Earnings', 'الأرباح المحتجزة', 'equity', 19, 'credit'),
('3210', 'Current Year Earnings', 'أرباح السنة الحالية', 'equity', 22, 'credit'),

-- الإيرادات (Revenue)
('4000', 'Revenue', 'الإيرادات', 'revenue', NULL, 'credit'),
('4100', 'Operating Revenue', 'إيرادات التشغيل', 'revenue', 24, 'credit'),
('4110', 'Transfer Fees Revenue', 'إيرادات رسوم التحويلات', 'revenue', 25, 'credit'),
('4111', 'Outgoing Transfer Fees', 'رسوم التحويلات الصادرة', 'revenue', 26, 'credit'),
('4112', 'Incoming Transfer Fees', 'رسوم التحويلات الواردة', 'revenue', 26, 'credit'),
('4120', 'Exchange Rate Gain', 'أرباح فروق أسعار الصرف', 'revenue', 25, 'credit'),
('4200', 'Other Revenue', 'إيرادات أخرى', 'revenue', 24, 'credit'),
('4210', 'Interest Income', 'إيرادات الفوائد', 'revenue', 30, 'credit'),

-- المصروفات (Expenses)
('5000', 'Expenses', 'المصروفات', 'expense', NULL, 'debit'),
('5100', 'Operating Expenses', 'مصروفات التشغيل', 'expense', 32, 'debit'),
('5110', 'Agent Commissions', 'عمولات الوكلاء', 'expense', 33, 'debit'),
('5111', 'Outgoing Transfer Commissions', 'عمولات التحويلات الصادرة', 'expense', 34, 'debit'),
('5112', 'Incoming Transfer Commissions', 'عمولات التحويلات الواردة', 'expense', 34, 'debit'),
('5120', 'Bank Charges', 'رسوم بنكية', 'expense', 33, 'debit'),
('5130', 'Exchange Rate Loss', 'خسائر فروق أسعار الصرف', 'expense', 33, 'debit'),
('5200', 'Administrative Expenses', 'مصروفات إدارية', 'expense', 32, 'debit'),
('5210', 'Salaries and Wages', 'الرواتب والأجور', 'expense', 39, 'debit'),
('5220', 'Rent Expense', 'مصروف الإيجار', 'expense', 39, 'debit'),
('5230', 'Utilities Expense', 'مصروف المرافق', 'expense', 39, 'debit'),
('5240', 'Office Supplies', 'مستلزمات المكتب', 'expense', 39, 'debit'),
('5250', 'Communication Expense', 'مصروف الاتصالات', 'expense', 39, 'debit'),
('5260', 'Maintenance Expense', 'مصروف الصيانة', 'expense', 39, 'debit');

-- جدول سجل الأنشطة
CREATE TABLE activity_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    action VARCHAR(100) NOT NULL,
    description TEXT NOT NULL,
    related_id INT,
    related_type VARCHAR(50),
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- جدول إعادة تعيين كلمة المرور
CREATE TABLE password_resets (
    id INT PRIMARY KEY AUTO_INCREMENT,
    email VARCHAR(100) NOT NULL,
    token VARCHAR(100) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_token (token),
    INDEX idx_expires (expires_at)
);

-- أسعار الصرف الأساسية (أمثلة)
INSERT INTO exchange_rates (from_currency, to_currency, rate, effective_date, created_by_user_id) VALUES
('USD', 'SAR', 3.75, CURDATE(), 1),
('SAR', 'USD', 0.2667, CURDATE(), 1),
('EUR', 'SAR', 4.10, CURDATE(), 1),
('SAR', 'EUR', 0.2439, CURDATE(), 1),
('GBP', 'SAR', 4.75, CURDATE(), 1),
('SAR', 'GBP', 0.2105, CURDATE(), 1),
('AED', 'SAR', 1.02, CURDATE(), 1),
('SAR', 'AED', 0.98, CURDATE(), 1),
('KWD', 'SAR', 12.25, CURDATE(), 1),
('SAR', 'KWD', 0.0816, CURDATE(), 1);
