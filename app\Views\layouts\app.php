<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? 'Trust Plus' ?></title>
    
    <!-- Bootstrap CSS (RTL) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="<?= $_SERVER['REQUEST_SCHEME'] ?>://<?= $_SERVER['HTTP_HOST'] ?>/Trust_Plus/public/css/style.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 2px 0 5px rgba(0,0,0,0.1);
        }
        
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            margin: 2px 10px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.1);
            transform: translateX(-5px);
        }
        
        .main-content {
            background-color: white;
            min-height: 100vh;
            border-radius: 15px 0 0 0;
            box-shadow: -2px 0 10px rgba(0,0,0,0.1);
        }
        
        .navbar {
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-radius: 0 0 15px 15px;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 10px 25px;
            font-weight: 600;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .table {
            border-radius: 10px;
            overflow: hidden;
        }
        
        .table thead th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            font-weight: 600;
        }
        
        .badge {
            border-radius: 20px;
            padding: 8px 15px;
            font-weight: 500;
        }
        
        .alert {
            border: none;
            border-radius: 10px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
        }
        
        .stats-card .stats-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .stats-card .stats-label {
            font-size: 1.1rem;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 px-0">
                <div class="sidebar">
                    <div class="p-4 text-center">
                        <h4 class="text-white mb-0">
                            <i class="fas fa-exchange-alt me-2"></i>
                            Trust Plus
                        </h4>
                        <small class="text-white-50">نظام إدارة التحويلات</small>
                    </div>
                    
                    <nav class="nav flex-column">
                        <a class="nav-link <?= strpos($_SERVER['REQUEST_URI'], '/dashboard') !== false ? 'active' : '' ?>" 
                           href="/Trust_Plus/dashboard">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            لوحة التحكم
                        </a>
                        
                        <?php if (AuthHelper::hasPermission('transfers_view')): ?>
                        <a class="nav-link <?= strpos($_SERVER['REQUEST_URI'], '/transfers') !== false ? 'active' : '' ?>" 
                           href="/Trust_Plus/transfers">
                            <i class="fas fa-exchange-alt me-2"></i>
                            التحويلات
                        </a>
                        <?php endif; ?>
                        
                        <?php if (AuthHelper::hasPermission('customers_view')): ?>
                        <a class="nav-link <?= strpos($_SERVER['REQUEST_URI'], '/customers') !== false ? 'active' : '' ?>" 
                           href="/Trust_Plus/customers">
                            <i class="fas fa-users me-2"></i>
                            العملاء
                        </a>
                        <?php endif; ?>
                        
                        <?php if (AuthHelper::hasPermission('accounting_view')): ?>
                        <div class="nav-item">
                            <a class="nav-link" data-bs-toggle="collapse" href="#accountingMenu" role="button">
                                <i class="fas fa-calculator me-2"></i>
                                المحاسبة
                                <i class="fas fa-chevron-down float-end mt-1"></i>
                            </a>
                            <div class="collapse" id="accountingMenu">
                                <a class="nav-link ps-4" href="/Trust_Plus/accounting/chart-of-accounts">
                                    <i class="fas fa-sitemap me-2"></i>
                                    شجرة الحسابات
                                </a>
                                <a class="nav-link ps-4" href="/Trust_Plus/accounting/journal">
                                    <i class="fas fa-book me-2"></i>
                                    قيود اليومية
                                </a>
                                <a class="nav-link ps-4" href="/Trust_Plus/accounting/ledger">
                                    <i class="fas fa-file-alt me-2"></i>
                                    دفتر الأستاذ
                                </a>
                            </div>
                        </div>
                        <?php endif; ?>
                        
                        <?php if (AuthHelper::hasPermission('reports_view')): ?>
                        <a class="nav-link <?= strpos($_SERVER['REQUEST_URI'], '/reports') !== false ? 'active' : '' ?>" 
                           href="/Trust_Plus/reports/transfers">
                            <i class="fas fa-chart-bar me-2"></i>
                            التقارير
                        </a>
                        <?php endif; ?>
                        
                        <?php if (AuthHelper::hasPermission('users_view')): ?>
                        <a class="nav-link <?= strpos($_SERVER['REQUEST_URI'], '/users') !== false ? 'active' : '' ?>" 
                           href="/Trust_Plus/users">
                            <i class="fas fa-user-cog me-2"></i>
                            المستخدمين
                        </a>
                        <?php endif; ?>
                        
                        <?php if (AuthHelper::hasPermission('settings_view')): ?>
                        <div class="nav-item">
                            <a class="nav-link" data-bs-toggle="collapse" href="#settingsMenu" role="button">
                                <i class="fas fa-cog me-2"></i>
                                الإعدادات
                                <i class="fas fa-chevron-down float-end mt-1"></i>
                            </a>
                            <div class="collapse" id="settingsMenu">
                                <a class="nav-link ps-4" href="/Trust_Plus/settings/currencies">
                                    <i class="fas fa-coins me-2"></i>
                                    العملات
                                </a>
                                <a class="nav-link ps-4" href="/Trust_Plus/settings/exchange-rates">
                                    <i class="fas fa-chart-line me-2"></i>
                                    أسعار الصرف
                                </a>
                                <a class="nav-link ps-4" href="/Trust_Plus/settings/branches">
                                    <i class="fas fa-building me-2"></i>
                                    الفروع
                                </a>
                            </div>
                        </div>
                        <?php endif; ?>
                    </nav>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 px-0">
                <div class="main-content">
                    <!-- Top Navbar -->
                    <nav class="navbar navbar-expand-lg navbar-light">
                        <div class="container-fluid">
                            <h5 class="mb-0"><?= $title ?? 'Trust Plus' ?></h5>
                            
                            <div class="navbar-nav ms-auto">
                                <div class="nav-item dropdown">
                                    <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" 
                                       id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                        <i class="fas fa-user-circle me-2"></i>
                                        <?= AuthHelper::getCurrentUser()['first_name'] ?? 'المستخدم' ?>
                                    </a>
                                    <ul class="dropdown-menu dropdown-menu-end">
                                        <li><a class="dropdown-item" href="/Trust_Plus/profile">
                                            <i class="fas fa-user me-2"></i>الملف الشخصي
                                        </a></li>
                                        <li><a class="dropdown-item" href="/Trust_Plus/auth/change-password">
                                            <i class="fas fa-key me-2"></i>تغيير كلمة المرور
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item" href="/Trust_Plus/logout">
                                            <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                                        </a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </nav>
                    
                    <!-- Page Content -->
                    <div class="container-fluid p-4">
                        <?php if (isset($flash) && $flash): ?>
                        <div class="alert alert-<?= $flash['type'] === 'error' ? 'danger' : $flash['type'] ?> alert-dismissible fade show" role="alert">
                            <?= FormatHelper::clean($flash['message']) ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        <?php endif; ?>
                        
                        <?= $content ?? '' ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom JS -->
    <script src="<?= $_SERVER['REQUEST_SCHEME'] ?>://<?= $_SERVER['HTTP_HOST'] ?>/Trust_Plus/public/js/script.js"></script>
    
    <script>
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 5000);
        
        // Confirm delete actions
        $('.btn-delete').on('click', function(e) {
            if (!confirm('هل أنت متأكد من الحذف؟')) {
                e.preventDefault();
            }
        });
        
        // Format numbers
        $('.format-number').each(function() {
            let number = parseFloat($(this).text());
            if (!isNaN(number)) {
                $(this).text(number.toLocaleString('ar-SA'));
            }
        });
    </script>
</body>
</html>
