<?php
/**
 * فئة الطلب
 * Request Class
 */

class Request
{
    private $data;
    private $files;
    
    public function __construct()
    {
        $this->data = array_merge($_GET, $_POST);
        $this->files = $_FILES;
    }
    
    public function get($key, $default = null)
    {
        return isset($this->data[$key]) ? $this->data[$key] : $default;
    }
    
    public function has($key)
    {
        return isset($this->data[$key]);
    }
    
    public function all()
    {
        return $this->data;
    }
    
    public function only($keys)
    {
        $result = [];
        foreach ($keys as $key) {
            if ($this->has($key)) {
                $result[$key] = $this->get($key);
            }
        }
        return $result;
    }
    
    public function except($keys)
    {
        $result = $this->data;
        foreach ($keys as $key) {
            unset($result[$key]);
        }
        return $result;
    }
    
    public function file($key)
    {
        return isset($this->files[$key]) ? $this->files[$key] : null;
    }
    
    public function hasFile($key)
    {
        return isset($this->files[$key]) && $this->files[$key]['error'] === UPLOAD_ERR_OK;
    }
    
    public function isPost()
    {
        return $_SERVER['REQUEST_METHOD'] === 'POST';
    }
    
    public function isGet()
    {
        return $_SERVER['REQUEST_METHOD'] === 'GET';
    }
    
    public function isAjax()
    {
        return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }
    
    public function validate($rules)
    {
        $errors = [];
        
        foreach ($rules as $field => $rule) {
            $value = $this->get($field);
            $ruleArray = explode('|', $rule);
            
            foreach ($ruleArray as $singleRule) {
                $error = $this->validateField($field, $value, $singleRule);
                if ($error) {
                    $errors[$field][] = $error;
                }
            }
        }
        
        return $errors;
    }
    
    private function validateField($field, $value, $rule)
    {
        if ($rule === 'required' && empty($value)) {
            return "حقل {$field} مطلوب";
        }
        
        if (strpos($rule, 'min:') === 0 && strlen($value) < substr($rule, 4)) {
            return "حقل {$field} يجب أن يكون أكثر من " . substr($rule, 4) . " أحرف";
        }
        
        if (strpos($rule, 'max:') === 0 && strlen($value) > substr($rule, 4)) {
            return "حقل {$field} يجب أن يكون أقل من " . substr($rule, 4) . " أحرف";
        }
        
        if ($rule === 'email' && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
            return "حقل {$field} يجب أن يكون بريد إلكتروني صحيح";
        }
        
        if ($rule === 'numeric' && !is_numeric($value)) {
            return "حقل {$field} يجب أن يكون رقم";
        }
        
        return null;
    }
}
