<?php
/**
 * دوال مساعدة للمصادقة
 * Authentication Helper Functions
 */

class AuthHelper
{
    /**
     * التحقق من تسجيل دخول المستخدم
     */
    public static function isLoggedIn()
    {
        return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
    }
    
    /**
     * الحصول على معرف المستخدم الحالي
     */
    public static function getCurrentUserId()
    {
        return self::isLoggedIn() ? $_SESSION['user_id'] : null;
    }
    
    /**
     * الحصول على بيانات المستخدم الحالي
     */
    public static function getCurrentUser()
    {
        if (!self::isLoggedIn()) {
            return null;
        }
        
        $db = Database::getInstance();
        return $db->fetch(
            "SELECT u.*, r.name as role_name, r.permissions, b.name as branch_name 
             FROM users u 
             LEFT JOIN roles r ON u.role_id = r.id 
             LEFT JOIN branches b ON u.branch_id = b.id 
             WHERE u.id = ?",
            [$_SESSION['user_id']]
        );
    }
    
    /**
     * التحقق من صلاحية المستخدم
     */
    public static function hasPermission($permission)
    {
        $user = self::getCurrentUser();
        if (!$user) {
            return false;
        }
        
        $permissions = json_decode($user['permissions'], true);
        return in_array($permission, $permissions) || in_array('all', $permissions);
    }
    
    /**
     * التحقق من كون المستخدم مدير
     */
    public static function isAdmin()
    {
        return self::hasPermission('admin') || self::hasPermission('all');
    }
    
    /**
     * تسجيل دخول المستخدم
     */
    public static function login($userId, $remember = false)
    {
        $_SESSION['user_id'] = $userId;
        $_SESSION['login_time'] = time();
        
        if ($remember) {
            // إنشاء token للتذكر
            $token = bin2hex(random_bytes(32));
            $db = Database::getInstance();
            $db->update('users', 
                ['remember_token' => $token], 
                'id = ?', 
                [$userId]
            );
            
            // إنشاء cookie
            setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/'); // 30 يوم
        }
    }
    
    /**
     * تسجيل خروج المستخدم
     */
    public static function logout()
    {
        // حذف remember token
        if (self::isLoggedIn()) {
            $db = Database::getInstance();
            $db->update('users', 
                ['remember_token' => null], 
                'id = ?', 
                [$_SESSION['user_id']]
            );
        }
        
        // حذف الجلسة
        session_destroy();
        
        // حذف cookie
        if (isset($_COOKIE['remember_token'])) {
            setcookie('remember_token', '', time() - 3600, '/');
        }
    }
    
    /**
     * التحقق من remember token
     */
    public static function checkRememberToken()
    {
        if (!self::isLoggedIn() && isset($_COOKIE['remember_token'])) {
            $db = Database::getInstance();
            $user = $db->fetch(
                "SELECT id FROM users WHERE remember_token = ? AND is_active = 1",
                [$_COOKIE['remember_token']]
            );
            
            if ($user) {
                self::login($user['id']);
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * تشفير كلمة المرور
     */
    public static function hashPassword($password)
    {
        return password_hash($password, PASSWORD_DEFAULT);
    }
    
    /**
     * التحقق من كلمة المرور
     */
    public static function verifyPassword($password, $hash)
    {
        return password_verify($password, $hash);
    }
    
    /**
     * إعادة توجيه إذا لم يكن مسجل دخول
     */
    public static function requireLogin()
    {
        if (!self::isLoggedIn() && !self::checkRememberToken()) {
            header('Location: /Trust_Plus/login');
            exit;
        }
    }
    
    /**
     * إعادة توجيه إذا لم يكن لديه صلاحية
     */
    public static function requirePermission($permission)
    {
        self::requireLogin();
        
        if (!self::hasPermission($permission)) {
            header('HTTP/1.1 403 Forbidden');
            echo "ليس لديك صلاحية للوصول لهذه الصفحة";
            exit;
        }
    }
}
