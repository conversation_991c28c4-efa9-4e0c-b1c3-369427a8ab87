<?php
/**
 * اختبار بسيط للنظام
 */
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار Trust Plus</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 40px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #007bff; }
        .test-item {
            padding: 10px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
            background: #f8f9fa;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار نظام Trust Plus</h1>
        
        <div class="test-item">
            <h3>✅ PHP يعمل بشكل صحيح</h3>
            <p>إصدار PHP: <strong><?= phpversion() ?></strong></p>
            <p>الوقت الحالي: <strong><?= date('Y-m-d H:i:s') ?></strong></p>
        </div>
        
        <div class="test-item">
            <h3>📁 اختبار الملفات الأساسية</h3>
            <?php
            $files = [
                'index.php' => 'ملف الدخول الرئيسي',
                'app/Core/Database.php' => 'فئة قاعدة البيانات',
                'app/Controllers/AuthController.php' => 'وحدة تحكم المصادقة',
                'public/css/style.css' => 'ملف التنسيق',
                'public/js/script.js' => 'ملف JavaScript'
            ];
            
            foreach ($files as $file => $description) {
                $exists = file_exists($file);
                $status = $exists ? '<span class="success">✅ موجود</span>' : '<span class="error">❌ غير موجود</span>';
                echo "<p><strong>$description:</strong> $status</p>";
            }
            ?>
        </div>
        
        <div class="test-item">
            <h3>🗄️ اختبار قاعدة البيانات</h3>
            <?php
            try {
                $config = require 'app/Config/database.php';
                $dsn = "mysql:host={$config['host']};dbname={$config['dbname']};charset={$config['charset']}";
                $pdo = new PDO($dsn, $config['username'], $config['password'], $config['options']);
                
                echo '<p class="success">✅ الاتصال بقاعدة البيانات نجح</p>';
                
                // اختبار الجداول
                $tables = ['users', 'customers', 'transfers', 'currencies', 'chart_of_accounts'];
                foreach ($tables as $table) {
                    $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
                    $exists = $stmt->rowCount() > 0;
                    $status = $exists ? '<span class="success">✅</span>' : '<span class="error">❌</span>';
                    echo "<p>جدول $table: $status</p>";
                }
                
                // اختبار المستخدم الافتراضي
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM users WHERE username = 'admin'");
                $result = $stmt->fetch();
                $adminExists = $result['count'] > 0;
                $status = $adminExists ? '<span class="success">✅ موجود</span>' : '<span class="error">❌ غير موجود</span>';
                echo "<p>المستخدم الافتراضي (admin): $status</p>";
                
            } catch (Exception $e) {
                echo '<p class="error">❌ خطأ في قاعدة البيانات: ' . $e->getMessage() . '</p>';
                echo '<div style="background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;">';
                echo '<h4>خطوات حل المشكلة:</h4>';
                echo '<ol>';
                echo '<li>تأكد من تشغيل خدمة MySQL</li>';
                echo '<li>أنشئ قاعدة بيانات باسم: <code>trust_plus</code></li>';
                echo '<li>استورد ملف: <code>database.sql</code></li>';
                echo '<li>تحقق من إعدادات قاعدة البيانات في: <code>app/Config/database.php</code></li>';
                echo '</ol>';
                echo '</div>';
            }
            ?>
        </div>
        
        <div class="test-item">
            <h3>🔗 روابط النظام</h3>
            <p>جرب هذه الروابط للتأكد من عمل النظام:</p>
            <a href="index.php" class="btn">🏠 الصفحة الرئيسية</a>
            <a href="index.php?route=login" class="btn">🔐 تسجيل الدخول</a>
            <a href="index.php?route=dashboard" class="btn">📊 لوحة التحكم</a>
        </div>
        
        <div class="test-item">
            <h3>🔑 بيانات الدخول الافتراضية</h3>
            <p><strong>اسم المستخدم:</strong> admin</p>
            <p><strong>كلمة المرور:</strong> password</p>
        </div>
        
        <div class="test-item">
            <h3>📋 معلومات إضافية</h3>
            <p><strong>مسار المشروع:</strong> <?= __DIR__ ?></p>
            <p><strong>URL الحالي:</strong> <?= $_SERVER['REQUEST_URI'] ?></p>
            <p><strong>الخادم:</strong> <?= $_SERVER['SERVER_SOFTWARE'] ?? 'غير محدد' ?></p>
        </div>
    </div>
</body>
</html>
