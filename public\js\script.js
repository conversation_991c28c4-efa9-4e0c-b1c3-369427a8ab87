/**
 * Trust Plus JavaScript Functions
 */

// Global variables
let currentUser = null;
let appConfig = {
    baseUrl: '/Trust_Plus',
    apiUrl: '/Trust_Plus/api',
    locale: 'ar',
    currency: 'SAR'
};

// Initialize application
$(document).ready(function() {
    initializeApp();
    setupEventHandlers();
    loadUserData();
});

/**
 * Initialize application
 */
function initializeApp() {
    // Setup CSRF token for AJAX requests
    $.ajaxSetup({
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    });
    
    // Initialize tooltips
    $('[data-bs-toggle="tooltip"]').tooltip();
    
    // Initialize popovers
    $('[data-bs-toggle="popover"]').popover();
    
    // Auto-hide alerts
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);
    
    // Format numbers
    formatNumbers();
    
    // Setup form validation
    setupFormValidation();
}

/**
 * Setup event handlers
 */
function setupEventHandlers() {
    // Confirm delete actions
    $(document).on('click', '.btn-delete', function(e) {
        if (!confirm('هل أنت متأكد من الحذف؟')) {
            e.preventDefault();
        }
    });
    
    // Handle form submissions
    $(document).on('submit', '.ajax-form', function(e) {
        e.preventDefault();
        submitAjaxForm($(this));
    });
    
    // Handle search inputs
    $(document).on('input', '.search-input', function() {
        const query = $(this).val();
        const target = $(this).data('target');
        
        if (query.length >= 2) {
            performSearch(query, target);
        }
    });
    
    // Handle currency conversion
    $(document).on('change', '.currency-select', function() {
        updateExchangeRate();
    });
    
    // Handle amount calculations
    $(document).on('input', '.amount-input', function() {
        calculateAmounts();
    });
    
    // Mobile sidebar toggle
    $(document).on('click', '.sidebar-toggle', function() {
        $('.sidebar').toggleClass('show');
    });
    
    // Close sidebar on overlay click
    $(document).on('click', '.sidebar-overlay', function() {
        $('.sidebar').removeClass('show');
    });
}

/**
 * Load user data
 */
function loadUserData() {
    $.get(appConfig.baseUrl + '/auth/check')
        .done(function(response) {
            if (response.authenticated) {
                currentUser = response.user;
                updateUserInterface();
            }
        })
        .fail(function() {
            console.log('Failed to load user data');
        });
}

/**
 * Update user interface based on user data
 */
function updateUserInterface() {
    if (currentUser) {
        $('.user-name').text(currentUser.first_name + ' ' + currentUser.last_name);
        $('.user-role').text(currentUser.role_name_ar);
        $('.user-branch').text(currentUser.branch_name);
    }
}

/**
 * Format numbers in Arabic locale
 */
function formatNumbers() {
    $('.format-number').each(function() {
        const number = parseFloat($(this).text());
        if (!isNaN(number)) {
            $(this).text(number.toLocaleString('ar-SA'));
        }
    });
    
    $('.format-currency').each(function() {
        const amount = parseFloat($(this).data('amount'));
        const currency = $(this).data('currency') || 'SAR';
        
        if (!isNaN(amount)) {
            $(this).text(formatCurrency(amount, currency));
        }
    });
}

/**
 * Format currency amount
 */
function formatCurrency(amount, currency = 'SAR') {
    const symbols = {
        'SAR': 'ر.س',
        'USD': '$',
        'EUR': '€',
        'GBP': '£',
        'AED': 'د.إ',
        'KWD': 'د.ك',
        'BHD': 'د.ب',
        'QAR': 'ر.ق',
        'OMR': 'ر.ع',
        'JOD': 'د.أ'
    };
    
    const symbol = symbols[currency] || currency;
    return amount.toLocaleString('ar-SA', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }) + ' ' + symbol;
}

/**
 * Setup form validation
 */
function setupFormValidation() {
    // Real-time validation
    $(document).on('blur', '.form-control', function() {
        validateField($(this));
    });
    
    // Form submission validation
    $(document).on('submit', 'form', function(e) {
        const form = $(this);
        let isValid = true;
        
        form.find('.form-control[required]').each(function() {
            if (!validateField($(this))) {
                isValid = false;
            }
        });
        
        if (!isValid) {
            e.preventDefault();
            showAlert('يرجى تصحيح الأخطاء المدخلة', 'error');
        }
    });
}

/**
 * Validate individual field
 */
function validateField(field) {
    const value = field.val().trim();
    const type = field.attr('type');
    const required = field.prop('required');
    let isValid = true;
    let message = '';
    
    // Clear previous validation
    field.removeClass('is-invalid is-valid');
    field.siblings('.invalid-feedback').remove();
    
    // Required validation
    if (required && !value) {
        isValid = false;
        message = 'هذا الحقل مطلوب';
    }
    
    // Email validation
    if (type === 'email' && value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
            isValid = false;
            message = 'البريد الإلكتروني غير صحيح';
        }
    }
    
    // Phone validation
    if (field.hasClass('phone-input') && value) {
        const phoneRegex = /^[\+]?[0-9\s\-\(\)]{10,}$/;
        if (!phoneRegex.test(value)) {
            isValid = false;
            message = 'رقم الهاتف غير صحيح';
        }
    }
    
    // Number validation
    if (type === 'number' && value) {
        if (isNaN(value)) {
            isValid = false;
            message = 'يجب أن يكون رقم';
        }
    }
    
    // Apply validation result
    if (isValid) {
        field.addClass('is-valid');
    } else {
        field.addClass('is-invalid');
        field.after('<div class="invalid-feedback">' + message + '</div>');
    }
    
    return isValid;
}

/**
 * Submit AJAX form
 */
function submitAjaxForm(form) {
    const url = form.attr('action');
    const method = form.attr('method') || 'POST';
    const data = new FormData(form[0]);
    
    // Show loading state
    const submitBtn = form.find('[type="submit"]');
    const originalText = submitBtn.html();
    submitBtn.html('<span class="spinner"></span> جاري المعالجة...').prop('disabled', true);
    
    $.ajax({
        url: url,
        method: method,
        data: data,
        processData: false,
        contentType: false
    })
    .done(function(response) {
        if (response.success) {
            showAlert(response.message || 'تم الحفظ بنجاح', 'success');
            
            // Redirect if specified
            if (response.redirect) {
                setTimeout(function() {
                    window.location.href = response.redirect;
                }, 1500);
            }
            
            // Reset form if specified
            if (response.reset_form) {
                form[0].reset();
            }
        } else {
            showAlert(response.message || 'حدث خطأ', 'error');
            
            // Show field errors
            if (response.errors) {
                showFieldErrors(form, response.errors);
            }
        }
    })
    .fail(function() {
        showAlert('حدث خطأ في الاتصال', 'error');
    })
    .always(function() {
        // Restore button state
        submitBtn.html(originalText).prop('disabled', false);
    });
}

/**
 * Show field errors
 */
function showFieldErrors(form, errors) {
    Object.keys(errors).forEach(function(field) {
        const input = form.find('[name="' + field + '"]');
        if (input.length) {
            input.addClass('is-invalid');
            input.siblings('.invalid-feedback').remove();
            input.after('<div class="invalid-feedback">' + errors[field][0] + '</div>');
        }
    });
}

/**
 * Perform search
 */
function performSearch(query, target) {
    const url = appConfig.baseUrl + '/search/' + target;
    
    $.get(url, { q: query })
        .done(function(response) {
            if (response.success) {
                displaySearchResults(response.data, target);
            }
        })
        .fail(function() {
            console.log('Search failed');
        });
}

/**
 * Display search results
 */
function displaySearchResults(results, target) {
    const container = $('.search-results[data-target="' + target + '"]');
    container.empty();
    
    if (results.length > 0) {
        results.forEach(function(item) {
            const resultItem = $('<div class="search-result-item"></div>');
            resultItem.html(formatSearchResult(item, target));
            resultItem.on('click', function() {
                selectSearchResult(item, target);
            });
            container.append(resultItem);
        });
        container.show();
    } else {
        container.hide();
    }
}

/**
 * Format search result
 */
function formatSearchResult(item, target) {
    switch (target) {
        case 'customers':
            return '<div><strong>' + item.first_name + ' ' + item.last_name + '</strong><br>' +
                   '<small>' + item.phone + '</small></div>';
        case 'accounts':
            return '<div><strong>' + item.account_code + '</strong> - ' + item.account_name_ar + '</div>';
        default:
            return '<div>' + JSON.stringify(item) + '</div>';
    }
}

/**
 * Select search result
 */
function selectSearchResult(item, target) {
    // Trigger custom event
    $(document).trigger('searchResultSelected', [item, target]);
    
    // Hide results
    $('.search-results[data-target="' + target + '"]').hide();
}

/**
 * Update exchange rate
 */
function updateExchangeRate() {
    const fromCurrency = $('.from-currency').val();
    const toCurrency = $('.to-currency').val();
    
    if (fromCurrency && toCurrency && fromCurrency !== toCurrency) {
        $.get(appConfig.baseUrl + '/transfers/exchange-rate', {
            from: fromCurrency,
            to: toCurrency
        })
        .done(function(response) {
            if (response.success) {
                $('.exchange-rate').val(response.rate);
                calculateAmounts();
            }
        })
        .fail(function() {
            console.log('Failed to get exchange rate');
        });
    }
}

/**
 * Calculate amounts
 */
function calculateAmounts() {
    const sendingAmount = parseFloat($('.sending-amount').val()) || 0;
    const exchangeRate = parseFloat($('.exchange-rate').val()) || 1;
    const feeAmount = parseFloat($('.fee-amount').val()) || 0;
    
    const receivingAmount = sendingAmount * exchangeRate;
    const totalAmount = sendingAmount + feeAmount;
    
    $('.receiving-amount').val(receivingAmount.toFixed(2));
    $('.total-amount').val(totalAmount.toFixed(2));
    
    // Update display
    $('.receiving-amount-display').text(formatCurrency(receivingAmount, $('.to-currency').val()));
    $('.total-amount-display').text(formatCurrency(totalAmount, $('.from-currency').val()));
}

/**
 * Show alert message
 */
function showAlert(message, type = 'info') {
    const alertClass = type === 'error' ? 'alert-danger' : 'alert-' + type;
    const icon = getAlertIcon(type);
    
    const alert = $('<div class="alert ' + alertClass + ' alert-dismissible fade show" role="alert">' +
                   '<i class="' + icon + ' me-2"></i>' + message +
                   '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                   '</div>');
    
    // Remove existing alerts
    $('.alert').remove();
    
    // Add new alert
    $('.container-fluid').first().prepend(alert);
    
    // Auto-hide after 5 seconds
    setTimeout(function() {
        alert.fadeOut();
    }, 5000);
}

/**
 * Get alert icon
 */
function getAlertIcon(type) {
    const icons = {
        'success': 'fas fa-check-circle',
        'error': 'fas fa-exclamation-triangle',
        'warning': 'fas fa-exclamation-triangle',
        'info': 'fas fa-info-circle'
    };
    
    return icons[type] || icons['info'];
}

/**
 * Confirm action
 */
function confirmAction(message, callback) {
    if (confirm(message)) {
        callback();
    }
}

/**
 * Load content via AJAX
 */
function loadContent(url, container) {
    $(container).html('<div class="text-center p-4"><span class="spinner"></span> جاري التحميل...</div>');
    
    $.get(url)
        .done(function(response) {
            $(container).html(response);
        })
        .fail(function() {
            $(container).html('<div class="alert alert-danger">حدث خطأ في تحميل المحتوى</div>');
        });
}

/**
 * Export data
 */
function exportData(url, format = 'excel') {
    window.open(url + '?format=' + format, '_blank');
}

/**
 * Print content
 */
function printContent(selector = 'body') {
    const content = $(selector).html();
    const printWindow = window.open('', '_blank');
    
    printWindow.document.write(`
        <!DOCTYPE html>
        <html dir="rtl">
        <head>
            <title>طباعة</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
            <style>
                body { font-family: Arial, sans-serif; }
                @media print {
                    .no-print { display: none !important; }
                }
            </style>
        </head>
        <body>
            ${content}
        </body>
        </html>
    `);
    
    printWindow.document.close();
    printWindow.print();
}

// Utility functions
window.TrustPlus = {
    formatCurrency: formatCurrency,
    showAlert: showAlert,
    confirmAction: confirmAction,
    loadContent: loadContent,
    exportData: exportData,
    printContent: printContent
};
