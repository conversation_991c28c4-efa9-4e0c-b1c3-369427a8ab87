<?php
/**
 * نموذج الحساب (شجرة الحسابات)
 * Account Model (Chart of Accounts)
 */

require_once 'BaseModel.php';

class Account extends BaseModel
{
    protected $table = 'chart_of_accounts';
    protected $fillable = [
        'account_code', 'account_name', 'account_name_ar', 'account_type',
        'parent_account_id', 'normal_balance', 'is_active'
    ];
    
    /**
     * البحث عن حساب بالكود
     */
    public function findByCode($accountCode)
    {
        return $this->findBy('account_code', $accountCode);
    }
    
    /**
     * الحصول على شجرة الحسابات الكاملة
     */
    public function getChartOfAccounts()
    {
        $sql = "SELECT a.*, p.account_name as parent_account_name
                FROM chart_of_accounts a
                LEFT JOIN chart_of_accounts p ON a.parent_account_id = p.id
                WHERE a.is_active = 1
                ORDER BY a.account_code";
        
        return $this->db->fetchAll($sql);
    }
    
    /**
     * الحصول على الحسابات الرئيسية (بدون parent)
     */
    public function getMainAccounts()
    {
        return $this->where(['parent_account_id' => null, 'is_active' => 1], 'account_code');
    }
    
    /**
     * الحصول على الحسابات الفرعية لحساب معين
     */
    public function getSubAccounts($parentId)
    {
        return $this->where(['parent_account_id' => $parentId, 'is_active' => 1], 'account_code');
    }
    
    /**
     * الحصول على الحسابات حسب النوع
     */
    public function getByType($accountType)
    {
        return $this->where(['account_type' => $accountType, 'is_active' => 1], 'account_code');
    }
    
    /**
     * الحصول على حساب مع رصيده
     */
    public function getAccountWithBalance($accountId, $currencyCode = 'SAR')
    {
        $sql = "SELECT a.*, 
                       COALESCE(ab.debit_balance, 0) as debit_balance,
                       COALESCE(ab.credit_balance, 0) as credit_balance,
                       COALESCE(ab.net_balance, 0) as net_balance
                FROM chart_of_accounts a
                LEFT JOIN account_balances ab ON a.id = ab.account_id AND ab.currency_code = ?
                WHERE a.id = ?";
        
        return $this->db->fetch($sql, [$currencyCode, $accountId]);
    }
    
    /**
     * الحصول على جميع الحسابات مع أرصدتها
     */
    public function getAllAccountsWithBalances($currencyCode = 'SAR')
    {
        $sql = "SELECT a.*, 
                       COALESCE(ab.debit_balance, 0) as debit_balance,
                       COALESCE(ab.credit_balance, 0) as credit_balance,
                       COALESCE(ab.net_balance, 0) as net_balance,
                       p.account_name as parent_account_name
                FROM chart_of_accounts a
                LEFT JOIN account_balances ab ON a.id = ab.account_id AND ab.currency_code = ?
                LEFT JOIN chart_of_accounts p ON a.parent_account_id = p.id
                WHERE a.is_active = 1
                ORDER BY a.account_code";
        
        return $this->db->fetchAll($sql, [$currencyCode]);
    }
    
    /**
     * حساب رصيد حساب معين من قيود اليومية
     */
    public function calculateBalance($accountId, $currencyCode = 'SAR', $toDate = null)
    {
        $sql = "SELECT 
                    SUM(jed.debit_amount) as total_debit,
                    SUM(jed.credit_amount) as total_credit
                FROM journal_entry_details jed
                JOIN journal_entries je ON jed.journal_entry_id = je.id
                WHERE jed.account_id = ? AND jed.currency_code = ? AND je.is_approved = 1";
        
        $params = [$accountId, $currencyCode];
        
        if ($toDate) {
            $sql .= " AND je.entry_date <= ?";
            $params[] = $toDate;
        }
        
        $result = $this->db->fetch($sql, $params);
        
        $totalDebit = $result['total_debit'] ?? 0;
        $totalCredit = $result['total_credit'] ?? 0;
        
        // الحصول على طبيعة الحساب
        $account = $this->find($accountId);
        $normalBalance = $account['normal_balance'];
        
        // حساب الرصيد الصافي حسب طبيعة الحساب
        if ($normalBalance === 'debit') {
            $netBalance = $totalDebit - $totalCredit;
        } else {
            $netBalance = $totalCredit - $totalDebit;
        }
        
        return [
            'debit_balance' => $totalDebit,
            'credit_balance' => $totalCredit,
            'net_balance' => $netBalance
        ];
    }
    
    /**
     * تحديث رصيد حساب في جدول الأرصدة
     */
    public function updateAccountBalance($accountId, $currencyCode = 'SAR')
    {
        $balance = $this->calculateBalance($accountId, $currencyCode);
        
        // التحقق من وجود سجل الرصيد
        $existingBalance = $this->db->fetch(
            "SELECT id FROM account_balances WHERE account_id = ? AND currency_code = ?",
            [$accountId, $currencyCode]
        );
        
        if ($existingBalance) {
            // تحديث الرصيد الموجود
            $this->db->update('account_balances', [
                'debit_balance' => $balance['debit_balance'],
                'credit_balance' => $balance['credit_balance'],
                'net_balance' => $balance['net_balance']
            ], 'id = ?', [$existingBalance['id']]);
        } else {
            // إنشاء سجل رصيد جديد
            $this->db->insert('account_balances', [
                'account_id' => $accountId,
                'currency_code' => $currencyCode,
                'debit_balance' => $balance['debit_balance'],
                'credit_balance' => $balance['credit_balance'],
                'net_balance' => $balance['net_balance']
            ]);
        }
        
        return $balance;
    }
    
    /**
     * الحصول على دفتر الأستاذ لحساب معين
     */
    public function getLedger($accountId, $currencyCode = 'SAR', $dateFrom = null, $dateTo = null)
    {
        $sql = "SELECT je.entry_date, je.description, je.entry_number,
                       jed.debit_amount, jed.credit_amount, jed.description as detail_description,
                       je.reference_type, je.reference_id
                FROM journal_entry_details jed
                JOIN journal_entries je ON jed.journal_entry_id = je.id
                WHERE jed.account_id = ? AND jed.currency_code = ? AND je.is_approved = 1";
        
        $params = [$accountId, $currencyCode];
        
        if ($dateFrom) {
            $sql .= " AND je.entry_date >= ?";
            $params[] = $dateFrom;
        }
        
        if ($dateTo) {
            $sql .= " AND je.entry_date <= ?";
            $params[] = $dateTo;
        }
        
        $sql .= " ORDER BY je.entry_date, je.id";
        
        $entries = $this->db->fetchAll($sql, $params);
        
        // حساب الرصيد المرحل
        $account = $this->find($accountId);
        $runningBalance = 0;
        
        foreach ($entries as &$entry) {
            if ($account['normal_balance'] === 'debit') {
                $runningBalance += $entry['debit_amount'] - $entry['credit_amount'];
            } else {
                $runningBalance += $entry['credit_amount'] - $entry['debit_amount'];
            }
            $entry['running_balance'] = $runningBalance;
        }
        
        return $entries;
    }
    
    /**
     * البحث في الحسابات
     */
    public function search($query)
    {
        $sql = "SELECT * FROM chart_of_accounts 
                WHERE (account_code LIKE ? OR account_name LIKE ? OR account_name_ar LIKE ?)
                AND is_active = 1
                ORDER BY account_code
                LIMIT 20";
        
        $searchTerm = "%{$query}%";
        return $this->db->fetchAll($sql, [$searchTerm, $searchTerm, $searchTerm]);
    }
    
    /**
     * التحقق من وجود كود حساب
     */
    public function codeExists($accountCode, $excludeId = null)
    {
        $sql = "SELECT COUNT(*) as count FROM chart_of_accounts WHERE account_code = ?";
        $params = [$accountCode];
        
        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        $result = $this->db->fetch($sql, $params);
        return $result['count'] > 0;
    }
    
    /**
     * الحصول على الحسابات النقدية
     */
    public function getCashAccounts()
    {
        $sql = "SELECT * FROM chart_of_accounts 
                WHERE account_code LIKE '111%' AND is_active = 1
                ORDER BY account_code";
        
        return $this->db->fetchAll($sql);
    }
    
    /**
     * الحصول على حسابات أموال العملاء
     */
    public function getCustomerFundsAccounts()
    {
        $sql = "SELECT * FROM chart_of_accounts 
                WHERE account_code LIKE '212%' AND is_active = 1
                ORDER BY account_code";
        
        return $this->db->fetchAll($sql);
    }
    
    /**
     * الحصول على حسابات الإيرادات
     */
    public function getRevenueAccounts()
    {
        return $this->getByType('revenue');
    }
    
    /**
     * الحصول على حسابات المصروفات
     */
    public function getExpenseAccounts()
    {
        return $this->getByType('expense');
    }
}
