<?php
/**
 * ملف اختبار للتأكد من عمل PHP والوصول للملفات
 */

echo "<h1>اختبار نظام Trust Plus</h1>";

// اختبار PHP
echo "<h2>معلومات PHP:</h2>";
echo "إصدار PHP: " . phpversion() . "<br>";
echo "الوقت الحالي: " . date('Y-m-d H:i:s') . "<br>";

// اختبار المسارات
echo "<h2>اختبار المسارات:</h2>";
echo "ROOT_PATH: " . __DIR__ . "<br>";
echo "APP_PATH: " . __DIR__ . "/app<br>";
echo "PUBLIC_PATH: " . __DIR__ . "/public<br>";
echo "STORAGE_PATH: " . __DIR__ . "/storage<br>";

// اختبار وجود الملفات
echo "<h2>اختبار وجود الملفات:</h2>";
$files = [
    'app/Core/Database.php',
    'app/Core/Router.php',
    'app/Controllers/AuthController.php',
    'public/css/style.css',
    'public/js/script.js'
];

foreach ($files as $file) {
    $exists = file_exists(__DIR__ . '/' . $file);
    echo $file . ": " . ($exists ? "✅ موجود" : "❌ غير موجود") . "<br>";
}

// اختبار قاعدة البيانات
echo "<h2>اختبار قاعدة البيانات:</h2>";
try {
    $config = require __DIR__ . '/app/Config/database.php';

    $dsn = "mysql:host={$config['host']};dbname={$config['dbname']};charset={$config['charset']}";
    $pdo = new PDO($dsn, $config['username'], $config['password'], $config['options']);
    echo "✅ الاتصال بقاعدة البيانات نجح<br>";

    // اختبار وجود الجداول
    $tables = ['users', 'customers', 'transfers', 'currencies', 'chart_of_accounts'];
    foreach ($tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        $exists = $stmt->rowCount() > 0;
        echo "جدول $table: " . ($exists ? "✅ موجود" : "❌ غير موجود") . "<br>";
    }

    // اختبار المستخدم الافتراضي
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users WHERE username = 'admin'");
    $result = $stmt->fetch();
    echo "المستخدم الافتراضي (admin): " . ($result['count'] > 0 ? "✅ موجود" : "❌ غير موجود") . "<br>";

} catch (Exception $e) {
    echo "❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "<br>";
    echo "<p style='color: red;'>تأكد من:</p>";
    echo "<ul>";
    echo "<li>تشغيل خدمة MySQL</li>";
    echo "<li>إنشاء قاعدة البيانات trust_plus</li>";
    echo "<li>استيراد ملف database.sql</li>";
    echo "</ul>";
}

// اختبار الصلاحيات
echo "<h2>اختبار صلاحيات الكتابة:</h2>";
$dirs = ['storage', 'storage/logs', 'storage/uploads'];
foreach ($dirs as $dir) {
    $writable = is_writable(__DIR__ . '/' . $dir);
    echo "مجلد $dir: " . ($writable ? "✅ قابل للكتابة" : "❌ غير قابل للكتابة") . "<br>";
}

echo "<h2>اختبار مكتمل!</h2>";
echo "<div style='margin: 20px 0; padding: 20px; background: #f0f8ff; border: 1px solid #0066cc; border-radius: 5px;'>";
echo "<h3>روابط مفيدة:</h3>";
echo "<p><a href='index.php' style='color: #0066cc; text-decoration: none; font-weight: bold;'>🏠 الانتقال إلى النظام الرئيسي</a></p>";
echo "<p><a href='index.php?route=login' style='color: #0066cc; text-decoration: none;'>🔐 صفحة تسجيل الدخول</a></p>";
echo "<p><a href='index.php?route=dashboard' style='color: #0066cc; text-decoration: none;'>📊 لوحة التحكم</a></p>";
echo "</div>";
echo "<div style='margin: 20px 0; padding: 15px; background: #fff3cd; border: 1px solid #ffc107; border-radius: 5px;'>";
echo "<h4>بيانات الدخول الافتراضية:</h4>";
echo "<p><strong>اسم المستخدم:</strong> admin</p>";
echo "<p><strong>كلمة المرور:</strong> password</p>";
echo "</div>";
?>
