<?php
/**
 * ملف اختبار للتأكد من عمل PHP والوصول للملفات
 */

echo "<h1>اختبار نظام Trust Plus</h1>";

// اختبار PHP
echo "<h2>معلومات PHP:</h2>";
echo "إصدار PHP: " . phpversion() . "<br>";
echo "الوقت الحالي: " . date('Y-m-d H:i:s') . "<br>";

// اختبار المسارات
echo "<h2>اختبار المسارات:</h2>";
echo "ROOT_PATH: " . __DIR__ . "<br>";
echo "APP_PATH: " . __DIR__ . "/app<br>";
echo "PUBLIC_PATH: " . __DIR__ . "/public<br>";
echo "STORAGE_PATH: " . __DIR__ . "/storage<br>";

// اختبار وجود الملفات
echo "<h2>اختبار وجود الملفات:</h2>";
$files = [
    'app/Core/Database.php',
    'app/Core/Router.php',
    'app/Controllers/AuthController.php',
    'public/css/style.css',
    'public/js/script.js'
];

foreach ($files as $file) {
    $exists = file_exists(__DIR__ . '/' . $file);
    echo $file . ": " . ($exists ? "✅ موجود" : "❌ غير موجود") . "<br>";
}

// اختبار قاعدة البيانات
echo "<h2>اختبار قاعدة البيانات:</h2>";
try {
    $config = [
        'host' => 'localhost',
        'dbname' => 'trust_plus',
        'username' => 'root',
        'password' => '',
        'charset' => 'utf8mb4'
    ];
    
    $dsn = "mysql:host={$config['host']};dbname={$config['dbname']};charset={$config['charset']}";
    $pdo = new PDO($dsn, $config['username'], $config['password']);
    echo "✅ الاتصال بقاعدة البيانات نجح<br>";
    
    // اختبار وجود الجداول
    $tables = ['users', 'customers', 'transfers', 'currencies'];
    foreach ($tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        $exists = $stmt->rowCount() > 0;
        echo "جدول $table: " . ($exists ? "✅ موجود" : "❌ غير موجود") . "<br>";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "<br>";
}

// اختبار الصلاحيات
echo "<h2>اختبار صلاحيات الكتابة:</h2>";
$dirs = ['storage', 'storage/logs', 'storage/uploads'];
foreach ($dirs as $dir) {
    $writable = is_writable(__DIR__ . '/' . $dir);
    echo "مجلد $dir: " . ($writable ? "✅ قابل للكتابة" : "❌ غير قابل للكتابة") . "<br>";
}

echo "<h2>اختبار مكتمل!</h2>";
echo "<p><a href='public/index.php'>الانتقال إلى النظام</a></p>";
?>
