<?php
/**
 * وحدة تحكم التحويلات
 * Transfer Controller
 */

require_once 'BaseController.php';

class TransferController extends BaseController
{
    private $transferModel;
    private $customerModel;
    private $currencyModel;
    private $countryModel;
    private $branchModel;
    private $journalEntryModel;
    
    public function __construct()
    {
        parent::__construct();
        $this->transferModel = new Transfer();
        $this->customerModel = new Customer();
        $this->currencyModel = new Currency();
        $this->countryModel = new Country();
        $this->branchModel = new Branch();
        $this->journalEntryModel = new JournalEntry();
    }
    
    /**
     * عرض قائمة جميع التحويلات
     */
    public function index()
    {
        $this->requirePermission('transfers_view');
        
        try {
            $page = $this->request->get('page', 1);
            $search = $this->request->get('search', '');
            $status = $this->request->get('status', '');
            $type = $this->request->get('type', '');
            $perPage = 20;
            
            $conditions = [];
            
            if (!empty($status)) {
                $conditions['status'] = $status;
            }
            
            if (!empty($type)) {
                $conditions['transfer_type'] = $type;
            }
            
            // تصفية حسب الفرع للمستخدمين غير المديرين
            $user = $this->getCurrentUser();
            if (!AuthHelper::hasPermission('admin') && $user['branch_id']) {
                $conditions['sending_branch_id'] = $user['branch_id'];
            }
            
            if (!empty($search)) {
                $transfers = $this->transferModel->search($search, 50);
                $pagination = null;
            } else {
                $result = $this->transferModel->paginate($page, $perPage, $conditions);
                $transfers = $result['data'];
                $pagination = $result;
            }
            
            // إضافة تفاصيل التحويلات
            foreach ($transfers as &$transfer) {
                $transfer = $this->transferModel->findWithDetails($transfer['id']);
            }
            
            $this->view('transfers/index', [
                'title' => 'إدارة التحويلات',
                'transfers' => $transfers,
                'pagination' => $pagination,
                'search' => $search,
                'status' => $status,
                'type' => $type,
                'flash' => $this->getFlashMessage()
            ]);
            
        } catch (Exception $e) {
            $this->handleError($e, 'حدث خطأ في تحميل قائمة التحويلات');
        }
    }
    
    /**
     * عرض التحويلات الصادرة
     */
    public function outgoing()
    {
        $this->requirePermission('transfers_view');
        
        try {
            $user = $this->getCurrentUser();
            $conditions = ['transfer_type' => 'outgoing'];
            
            if (!AuthHelper::hasPermission('admin') && $user['branch_id']) {
                $conditions['sending_branch_id'] = $user['branch_id'];
            }
            
            $transfers = $this->transferModel->getAllWithDetails($conditions);
            
            $this->view('transfers/outgoing', [
                'title' => 'التحويلات الصادرة',
                'transfers' => $transfers,
                'flash' => $this->getFlashMessage()
            ]);
            
        } catch (Exception $e) {
            $this->handleError($e, 'حدث خطأ في تحميل التحويلات الصادرة');
        }
    }
    
    /**
     * عرض التحويلات الواردة
     */
    public function incoming()
    {
        $this->requirePermission('transfers_view');
        
        try {
            $user = $this->getCurrentUser();
            $conditions = ['transfer_type' => 'incoming'];
            
            if (!AuthHelper::hasPermission('admin') && $user['branch_id']) {
                $conditions['receiving_branch_id'] = $user['branch_id'];
            }
            
            $transfers = $this->transferModel->getAllWithDetails($conditions);
            
            $this->view('transfers/incoming', [
                'title' => 'التحويلات الواردة',
                'transfers' => $transfers,
                'flash' => $this->getFlashMessage()
            ]);
            
        } catch (Exception $e) {
            $this->handleError($e, 'حدث خطأ في تحميل التحويلات الواردة');
        }
    }
    
    /**
     * عرض التحويلات المعلقة للموافقة
     */
    public function pendingApproval()
    {
        $this->requirePermission('transfers_approve');
        
        try {
            $user = $this->getCurrentUser();
            $branchId = AuthHelper::hasPermission('admin') ? null : $user['branch_id'];
            
            $transfers = $this->transferModel->getPendingApproval($branchId);
            
            $this->view('transfers/pending_approval', [
                'title' => 'التحويلات المعلقة للموافقة',
                'transfers' => $transfers,
                'flash' => $this->getFlashMessage()
            ]);
            
        } catch (Exception $e) {
            $this->handleError($e, 'حدث خطأ في تحميل التحويلات المعلقة');
        }
    }
    
    /**
     * عرض صفحة إنشاء تحويل جديد
     */
    public function create()
    {
        $this->requirePermission('transfers_create');
        
        try {
            $currencies = $this->currencyModel->getActiveCurrencies();
            $countries = $this->countryModel->getActiveCountries();
            $branches = $this->branchModel->getActiveBranches();
            
            // إذا تم تمرير معرف عميل
            $customerId = $this->request->get('customer_id');
            $customer = null;
            if ($customerId) {
                $customer = $this->customerModel->findWithDetails($customerId);
            }
            
            $this->view('transfers/create', [
                'title' => 'إنشاء تحويل جديد',
                'currencies' => $currencies,
                'countries' => $countries,
                'branches' => $branches,
                'customer' => $customer,
                'flash' => $this->getFlashMessage(),
                'errors' => $this->getValidationErrors(),
                'old' => $this->getOldInput()
            ]);
            
        } catch (Exception $e) {
            $this->handleError($e, 'حدث خطأ في تحميل صفحة إنشاء التحويل');
        }
    }
    
    /**
     * حفظ تحويل جديد
     */
    public function store()
    {
        $this->requirePermission('transfers_create');
        
        // التحقق من صحة البيانات
        $this->validate([
            'sender_customer_id' => 'required|numeric',
            'beneficiary_name' => 'required|min:2',
            'beneficiary_country_id' => 'required|numeric',
            'sending_amount' => 'required|numeric',
            'sending_currency_code' => 'required',
            'receiving_currency_code' => 'required',
            'exchange_rate' => 'required|numeric',
            'fee_amount' => 'required|numeric'
        ]);
        
        try {
            $data = $this->request->only([
                'sender_customer_id', 'beneficiary_name', 'beneficiary_phone', 'beneficiary_address',
                'beneficiary_city', 'beneficiary_country_id', 'beneficiary_bank_name',
                'beneficiary_account_number', 'beneficiary_iban', 'sending_amount',
                'sending_currency_code', 'receiving_currency_code', 'exchange_rate',
                'fee_amount', 'fee_currency_code', 'purpose', 'notes', 'transfer_type'
            ]);
            
            // حساب المبلغ المستلم والمبلغ الإجمالي
            $data['receiving_amount'] = $data['sending_amount'] * $data['exchange_rate'];
            $data['total_paid_by_sender'] = $data['sending_amount'] + $data['fee_amount'];
            
            // تعيين الفرع والمستخدم
            $user = $this->getCurrentUser();
            $data['sending_branch_id'] = $user['branch_id'];
            $data['created_by_user_id'] = $user['id'];
            
            // تعيين عملة الرسوم إذا لم تكن محددة
            if (empty($data['fee_currency_code'])) {
                $data['fee_currency_code'] = $data['sending_currency_code'];
            }
            
            // إنشاء التحويل
            $transferId = $this->transferModel->createTransfer($data);
            
            // إنشاء قيد محاسبي تلقائي
            if (AuthHelper::hasPermission('accounting_create')) {
                try {
                    $transferData = $this->transferModel->findWithDetails($transferId);
                    $this->journalEntryModel->createTransferEntry($transferId, $transferData);
                } catch (Exception $e) {
                    // تسجيل الخطأ ولكن لا نوقف العملية
                    error_log('خطأ في إنشاء القيد المحاسبي: ' . $e->getMessage());
                }
            }
            
            // تسجيل النشاط
            $this->logActivity('transfer_create', "إنشاء تحويل جديد رقم: {$data['transfer_number']}", $transferId, 'transfer');
            
            $this->redirect("/transfers/{$transferId}", 'تم إنشاء التحويل بنجاح', 'success');
            
        } catch (Exception $e) {
            $this->handleError($e, 'حدث خطأ أثناء إنشاء التحويل');
        }
    }
    
    /**
     * عرض تفاصيل تحويل
     */
    public function show($id)
    {
        $this->requirePermission('transfers_view');
        
        try {
            $transfer = $this->transferModel->findWithDetails($id);
            
            if (!$transfer) {
                $this->redirect('/transfers', 'التحويل غير موجود', 'error');
            }
            
            // التحقق من صلاحية الوصول للتحويل
            $user = $this->getCurrentUser();
            if (!AuthHelper::hasPermission('admin') && $user['branch_id']) {
                if ($transfer['sending_branch_id'] != $user['branch_id'] && 
                    $transfer['receiving_branch_id'] != $user['branch_id']) {
                    $this->redirect('/transfers', 'ليس لديك صلاحية لعرض هذا التحويل', 'error');
                }
            }
            
            $this->view('transfers/show', [
                'title' => 'تفاصيل التحويل',
                'transfer' => $transfer,
                'flash' => $this->getFlashMessage()
            ]);
            
        } catch (Exception $e) {
            $this->handleError($e, 'حدث خطأ في تحميل تفاصيل التحويل');
        }
    }
    
    /**
     * الموافقة على تحويل
     */
    public function approve($id)
    {
        $this->requirePermission('transfers_approve');
        
        try {
            $transfer = $this->transferModel->find($id);
            
            if (!$transfer) {
                $this->json(['success' => false, 'message' => 'التحويل غير موجود'], 404);
            }
            
            if ($transfer['status'] !== 'pending') {
                $this->json(['success' => false, 'message' => 'التحويل ليس في حالة انتظار'], 400);
            }
            
            // تحديث حالة التحويل
            $this->transferModel->updateStatus($id, 'approved', AuthHelper::getCurrentUserId());
            
            // تسجيل النشاط
            $this->logActivity('transfer_approve', "الموافقة على التحويل رقم: {$transfer['transfer_number']}", $id, 'transfer');
            
            $this->json([
                'success' => true,
                'message' => 'تم اعتماد التحويل بنجاح'
            ]);
            
        } catch (Exception $e) {
            $this->json(['success' => false, 'message' => 'حدث خطأ أثناء اعتماد التحويل'], 500);
        }
    }
    
    /**
     * رفض تحويل
     */
    public function reject($id)
    {
        $this->requirePermission('transfers_approve');
        
        try {
            $transfer = $this->transferModel->find($id);
            
            if (!$transfer) {
                $this->json(['success' => false, 'message' => 'التحويل غير موجود'], 404);
            }
            
            if ($transfer['status'] !== 'pending') {
                $this->json(['success' => false, 'message' => 'التحويل ليس في حالة انتظار'], 400);
            }
            
            $reason = $this->request->get('reason', '');
            
            // تحديث حالة التحويل
            $this->transferModel->updateStatus($id, 'rejected', AuthHelper::getCurrentUserId());
            
            // إضافة سبب الرفض في الملاحظات
            if (!empty($reason)) {
                $currentNotes = $transfer['notes'] ?? '';
                $newNotes = $currentNotes . "\nسبب الرفض: " . $reason;
                $this->transferModel->update($id, ['notes' => $newNotes]);
            }
            
            // تسجيل النشاط
            $this->logActivity('transfer_reject', "رفض التحويل رقم: {$transfer['transfer_number']}", $id, 'transfer');
            
            $this->json([
                'success' => true,
                'message' => 'تم رفض التحويل'
            ]);
            
        } catch (Exception $e) {
            $this->json(['success' => false, 'message' => 'حدث خطأ أثناء رفض التحويل'], 500);
        }
    }
    
    /**
     * الحصول على سعر الصرف (AJAX)
     */
    public function getExchangeRate()
    {
        try {
            $fromCurrency = $this->request->get('from');
            $toCurrency = $this->request->get('to');
            
            if (empty($fromCurrency) || empty($toCurrency)) {
                $this->json(['success' => false, 'message' => 'العملات مطلوبة'], 400);
            }
            
            $rate = $this->currencyModel->getExchangeRate($fromCurrency, $toCurrency);
            
            if ($rate === null) {
                $this->json(['success' => false, 'message' => 'سعر الصرف غير متوفر'], 404);
            }
            
            $this->json([
                'success' => true,
                'rate' => $rate
            ]);
            
        } catch (Exception $e) {
            $this->json(['success' => false, 'message' => 'حدث خطأ في الحصول على سعر الصرف'], 500);
        }
    }
}
