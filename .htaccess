RewriteEngine On

# السماح بالوصول للملفات الثابتة
RewriteCond %{REQUEST_FILENAME} -f
RewriteCond %{REQUEST_URI} \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ [NC]
RewriteRule ^(.*)$ public/$1 [L]

# إعادة توجيه جميع الطلبات الأخرى إلى public/index.php
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !^/public/
RewriteRule ^(.*)$ public/index.php [QSA,L]

# منع الوصول المباشر لملفات PHP خارج public
RewriteCond %{THE_REQUEST} \s/+([^/\s]*/)*(app|storage|vendor)/ [NC]
RewriteRule ^ - [F]

# إعدادات الأمان
<Files ~ "^\.">
    Require all denied
</Files>

<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|inc|bak)$">
    Require all denied
</FilesMatch>

# منع عرض محتويات المجلدات
Options -Indexes

# إعدادات PHP
<IfModule mod_php7.c>
    php_value upload_max_filesize 5M
    php_value post_max_size 5M
    php_value max_execution_time 300
    php_value memory_limit 256M
    php_value display_errors Off
    php_value log_errors On
</IfModule>

# إعدادات MIME
<IfModule mod_mime.c>
    AddType text/css .css
    AddType application/javascript .js
    AddType image/svg+xml .svg
</IfModule>

# ضغط الملفات
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# تخزين مؤقت للملفات الثابتة
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
</IfModule>
