RewriteEngine On

# إعادة توجيه جميع الطلبات إلى public/index.php
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ public/index.php [QSA,L]

# منع الوصول المباشر لملفات PHP خارج public
RewriteCond %{THE_REQUEST} \s/+([^/\s]*/)*(app|storage|vendor)/ [NC]
RewriteRule ^ - [F]

# إعدادات الأمان
<Files ~ "^\.">
    Order allow,deny
    Deny from all
</Files>

# منع عرض محتويات المجلدات
Options -Indexes

# إعدادات PHP
php_value upload_max_filesize 5M
php_value post_max_size 5M
php_value max_execution_time 300
php_value memory_limit 256M
