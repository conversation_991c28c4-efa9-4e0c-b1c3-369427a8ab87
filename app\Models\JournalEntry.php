<?php
/**
 * نموذج قيد اليومية
 * Journal Entry Model
 */

require_once 'BaseModel.php';

class JournalEntry extends BaseModel
{
    protected $table = 'journal_entries';
    protected $fillable = [
        'entry_number', 'entry_date', 'description', 'reference_type', 'reference_id',
        'total_amount', 'created_by_user_id', 'approved_by_user_id', 'approved_at', 'is_approved'
    ];
    
    /**
     * البحث عن قيد برقم القيد
     */
    public function findByEntryNumber($entryNumber)
    {
        return $this->findBy('entry_number', $entryNumber);
    }
    
    /**
     * الحصول على قيد مع تفاصيله
     */
    public function findWithDetails($id)
    {
        $sql = "SELECT je.*, 
                       cu.first_name as created_by_first_name, cu.last_name as created_by_last_name,
                       au.first_name as approved_by_first_name, au.last_name as approved_by_last_name
                FROM journal_entries je
                LEFT JOIN users cu ON je.created_by_user_id = cu.id
                LEFT JOIN users au ON je.approved_by_user_id = au.id
                WHERE je.id = ?";
        
        $entry = $this->db->fetch($sql, [$id]);
        
        if ($entry) {
            // الحصول على تفاصيل القيد
            $entry['details'] = $this->getEntryDetails($id);
        }
        
        return $entry;
    }
    
    /**
     * الحصول على تفاصيل قيد اليومية
     */
    public function getEntryDetails($entryId)
    {
        $sql = "SELECT jed.*, a.account_code, a.account_name, a.account_name_ar, c.symbol as currency_symbol
                FROM journal_entry_details jed
                JOIN chart_of_accounts a ON jed.account_id = a.id
                JOIN currencies c ON jed.currency_code = c.code
                WHERE jed.journal_entry_id = ?
                ORDER BY jed.id";
        
        return $this->db->fetchAll($sql, [$entryId]);
    }
    
    /**
     * إنشاء رقم قيد جديد
     */
    public function generateEntryNumber()
    {
        $prefix = 'JE';
        $year = date('Y');
        
        // البحث عن آخر رقم قيد للسنة
        $sql = "SELECT entry_number FROM journal_entries 
                WHERE entry_number LIKE ? 
                ORDER BY entry_number DESC 
                LIMIT 1";
        
        $pattern = $prefix . $year . '%';
        $lastEntry = $this->db->fetch($sql, [$pattern]);
        
        if ($lastEntry) {
            // استخراج الرقم التسلسلي وزيادته
            $lastNumber = substr($lastEntry['entry_number'], -6);
            $newNumber = str_pad((int)$lastNumber + 1, 6, '0', STR_PAD_LEFT);
        } else {
            $newNumber = '000001';
        }
        
        return $prefix . $year . $newNumber;
    }
    
    /**
     * إنشاء قيد يومية جديد مع تفاصيله
     */
    public function createJournalEntry($entryData, $details)
    {
        $this->db->beginTransaction();
        
        try {
            // التحقق من توازن القيد
            if (!$this->validateEntryBalance($details)) {
                throw new Exception('القيد غير متوازن - مجموع المدين يجب أن يساوي مجموع الدائن');
            }
            
            // إنشاء رقم القيد
            $entryData['entry_number'] = $this->generateEntryNumber();
            
            // حساب المبلغ الإجمالي
            $entryData['total_amount'] = $this->calculateTotalAmount($details);
            
            // إنشاء رأس القيد
            $entryId = $this->create($entryData);
            
            // إنشاء تفاصيل القيد
            foreach ($details as $detail) {
                $detail['journal_entry_id'] = $entryId;
                $this->db->insert('journal_entry_details', $detail);
            }
            
            $this->db->commit();
            return $entryId;
            
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }
    
    /**
     * التحقق من توازن القيد
     */
    private function validateEntryBalance($details)
    {
        $totalDebit = 0;
        $totalCredit = 0;
        
        foreach ($details as $detail) {
            $totalDebit += $detail['debit_amount'] ?? 0;
            $totalCredit += $detail['credit_amount'] ?? 0;
        }
        
        return abs($totalDebit - $totalCredit) < 0.01; // السماح بفرق صغير للتقريب
    }
    
    /**
     * حساب المبلغ الإجمالي للقيد
     */
    private function calculateTotalAmount($details)
    {
        $total = 0;
        foreach ($details as $detail) {
            $total += max($detail['debit_amount'] ?? 0, $detail['credit_amount'] ?? 0);
        }
        return $total / 2; // القسمة على 2 لأن كل مبلغ محسوب مرتين (مدين ودائن)
    }
    
    /**
     * الموافقة على قيد اليومية
     */
    public function approveEntry($entryId, $userId)
    {
        $this->db->beginTransaction();
        
        try {
            // تحديث حالة القيد
            $this->update($entryId, [
                'is_approved' => 1,
                'approved_by_user_id' => $userId,
                'approved_at' => date('Y-m-d H:i:s')
            ]);
            
            // تحديث أرصدة الحسابات المتأثرة
            $details = $this->getEntryDetails($entryId);
            $accountModel = new Account();
            
            foreach ($details as $detail) {
                $accountModel->updateAccountBalance($detail['account_id'], $detail['currency_code']);
            }
            
            $this->db->commit();
            return true;
            
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }
    
    /**
     * إلغاء الموافقة على قيد اليومية
     */
    public function unapproveEntry($entryId)
    {
        $this->db->beginTransaction();
        
        try {
            // تحديث حالة القيد
            $this->update($entryId, [
                'is_approved' => 0,
                'approved_by_user_id' => null,
                'approved_at' => null
            ]);
            
            // تحديث أرصدة الحسابات المتأثرة
            $details = $this->getEntryDetails($entryId);
            $accountModel = new Account();
            
            foreach ($details as $detail) {
                $accountModel->updateAccountBalance($detail['account_id'], $detail['currency_code']);
            }
            
            $this->db->commit();
            return true;
            
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }
    
    /**
     * الحصول على جميع قيود اليومية مع التفاصيل
     */
    public function getAllWithDetails($conditions = [], $orderBy = 'entry_date DESC, id DESC', $limit = null)
    {
        $sql = "SELECT je.*, 
                       cu.first_name as created_by_first_name, cu.last_name as created_by_last_name,
                       au.first_name as approved_by_first_name, au.last_name as approved_by_last_name
                FROM journal_entries je
                LEFT JOIN users cu ON je.created_by_user_id = cu.id
                LEFT JOIN users au ON je.approved_by_user_id = au.id";
        
        $params = [];
        if (!empty($conditions)) {
            $whereClause = [];
            foreach ($conditions as $column => $value) {
                if (is_array($value)) {
                    $operator = $value[0];
                    $val = $value[1];
                    $whereClause[] = "je.{$column} {$operator} ?";
                    $params[] = $val;
                } else {
                    $whereClause[] = "je.{$column} = ?";
                    $params[] = $value;
                }
            }
            $sql .= " WHERE " . implode(' AND ', $whereClause);
        }
        
        if ($orderBy) {
            $sql .= " ORDER BY {$orderBy}";
        }
        
        if ($limit) {
            $sql .= " LIMIT {$limit}";
        }
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * البحث في قيود اليومية
     */
    public function search($query, $limit = 20)
    {
        $sql = "SELECT je.*, 
                       cu.first_name as created_by_first_name, cu.last_name as created_by_last_name
                FROM journal_entries je
                LEFT JOIN users cu ON je.created_by_user_id = cu.id
                WHERE (je.entry_number LIKE ? OR je.description LIKE ?)
                ORDER BY je.entry_date DESC, je.id DESC
                LIMIT ?";
        
        $searchTerm = "%{$query}%";
        return $this->db->fetchAll($sql, [$searchTerm, $searchTerm, $limit]);
    }
    
    /**
     * الحصول على قيود اليومية حسب التاريخ
     */
    public function getByDateRange($dateFrom, $dateTo, $approved = null)
    {
        $conditions = [
            'entry_date' => ['>=', $dateFrom],
            'entry_date' => ['<=', $dateTo]
        ];
        
        if ($approved !== null) {
            $conditions['is_approved'] = $approved;
        }
        
        return $this->getAllWithDetails($conditions);
    }
    
    /**
     * الحصول على قيود اليومية المعلقة للموافقة
     */
    public function getPendingApproval()
    {
        return $this->getAllWithDetails(['is_approved' => 0], 'entry_date ASC, id ASC');
    }
    
    /**
     * إنشاء قيد تلقائي للتحويل
     */
    public function createTransferEntry($transferId, $transferData)
    {
        $entryData = [
            'entry_date' => date('Y-m-d'),
            'description' => "قيد تحويل رقم {$transferData['transfer_number']}",
            'reference_type' => 'transfer',
            'reference_id' => $transferId,
            'created_by_user_id' => $transferData['created_by_user_id']
        ];
        
        $details = [];
        
        if ($transferData['transfer_type'] === 'outgoing') {
            // تحويل صادر
            // مدين: النقدية (المبلغ الإجمالي المستلم من العميل)
            $details[] = [
                'account_id' => 3, // حساب النقدية
                'debit_amount' => $transferData['total_paid_by_sender'],
                'credit_amount' => 0,
                'currency_code' => $transferData['sending_currency_code'],
                'description' => "استلام مبلغ تحويل من {$transferData['sender_name']}"
            ];
            
            // دائن: أموال العملاء المعلقة (المبلغ الأساسي)
            $details[] = [
                'account_id' => 15, // حساب أموال التحويلات الصادرة
                'debit_amount' => 0,
                'credit_amount' => $transferData['sending_amount'],
                'currency_code' => $transferData['sending_currency_code'],
                'description' => "مبلغ تحويل للمستفيد {$transferData['beneficiary_name']}"
            ];
            
            // دائن: إيرادات رسوم التحويلات
            $details[] = [
                'account_id' => 27, // حساب رسوم التحويلات الصادرة
                'debit_amount' => 0,
                'credit_amount' => $transferData['fee_amount'],
                'currency_code' => $transferData['fee_currency_code'],
                'description' => "رسوم تحويل رقم {$transferData['transfer_number']}"
            ];
        }
        
        return $this->createJournalEntry($entryData, $details);
    }
}
