<?php
ob_start();
?>

<!-- Stats Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="stats-card">
            <div class="stats-number"><?= $stats['today_transfers'] ?></div>
            <div class="stats-label">
                <i class="fas fa-exchange-alt me-2"></i>
                تحويلات اليوم
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);">
            <div class="stats-number"><?= FormatHelper::formatMoney($stats['today_amount']) ?></div>
            <div class="stats-label">
                <i class="fas fa-dollar-sign me-2"></i>
                مبلغ اليوم
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);">
            <div class="stats-number"><?= $stats['pending_transfers'] ?></div>
            <div class="stats-label">
                <i class="fas fa-clock me-2"></i>
                تحويلات معلقة
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); color: #333;">
            <div class="stats-number"><?= $stats['total_customers'] ?></div>
            <div class="stats-label">
                <i class="fas fa-users me-2"></i>
                إجمالي العملاء
            </div>
        </div>
    </div>
</div>

<!-- Charts and Recent Activity -->
<div class="row">
    <!-- Monthly Performance Chart -->
    <div class="col-md-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    الأداء الشهري
                </h5>
            </div>
            <div class="card-body">
                <canvas id="monthlyChart" height="100"></canvas>
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <?php if (AuthHelper::hasPermission('transfers_create')): ?>
                <a href="index.php?route=transfers&action=create" class="btn btn-primary w-100 mb-3">
                    <i class="fas fa-plus me-2"></i>
                    تحويل جديد
                </a>
                <?php endif; ?>

                <?php if (AuthHelper::hasPermission('customers_create')): ?>
                <a href="index.php?route=customers&action=create" class="btn btn-success w-100 mb-3">
                    <i class="fas fa-user-plus me-2"></i>
                    عميل جديد
                </a>
                <?php endif; ?>

                <?php if (AuthHelper::hasPermission('reports_view')): ?>
                <a href="index.php?route=reports" class="btn btn-info w-100 mb-3">
                    <i class="fas fa-chart-bar me-2"></i>
                    التقارير
                </a>
                <?php endif; ?>

                <?php if (AuthHelper::hasPermission('accounting_view')): ?>
                <a href="index.php?route=accounting&action=journal" class="btn btn-warning w-100">
                    <i class="fas fa-book me-2"></i>
                    قيود اليومية
                </a>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Recent Transfers and Pending Approvals -->
<div class="row">
    <!-- Recent Transfers -->
    <div class="col-md-8 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>
                    التحويلات الأخيرة
                </h5>
                <a href="index.php?route=transfers" class="btn btn-sm btn-outline-primary">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                <?php if (!empty($recentTransfers)): ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>رقم التحويل</th>
                                <th>المرسل</th>
                                <th>المستفيد</th>
                                <th>المبلغ</th>
                                <th>الحالة</th>
                                <th>التاريخ</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach (array_slice($recentTransfers, 0, 5) as $transfer): ?>
                            <tr>
                                <td>
                                    <a href="index.php?route=transfers&action=show&id=<?= $transfer['id'] ?>" class="text-decoration-none">
                                        <?= $transfer['transfer_number'] ?>
                                    </a>
                                </td>
                                <td><?= $transfer['sender_first_name'] . ' ' . $transfer['sender_last_name'] ?></td>
                                <td><?= FormatHelper::truncate($transfer['beneficiary_name'], 20) ?></td>
                                <td><?= FormatHelper::formatMoney($transfer['sending_amount'], $transfer['sending_currency_code']) ?></td>
                                <td><?= FormatHelper::formatTransferStatusBadge($transfer['status']) ?></td>
                                <td><?= FormatHelper::formatDate($transfer['created_at'], 'd/m/Y') ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="text-center py-4">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <p class="text-muted">لا توجد تحويلات حديثة</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Pending Approvals -->
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-clock me-2"></i>
                    في انتظار الموافقة
                </h5>
            </div>
            <div class="card-body">
                <?php if (!empty($pendingTransfers) && AuthHelper::hasPermission('transfers_approve')): ?>
                <div class="list-group list-group-flush">
                    <?php foreach (array_slice($pendingTransfers, 0, 5) as $transfer): ?>
                    <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                        <div>
                            <h6 class="mb-1"><?= $transfer['transfer_number'] ?></h6>
                            <small class="text-muted">
                                <?= $transfer['sender_first_name'] . ' ' . $transfer['sender_last_name'] ?>
                            </small>
                        </div>
                        <div class="text-end">
                            <div class="small text-muted">
                                <?= FormatHelper::formatMoney($transfer['sending_amount'], $transfer['sending_currency_code']) ?>
                            </div>
                            <div class="btn-group btn-group-sm mt-1">
                                <button class="btn btn-success btn-sm approve-transfer" data-id="<?= $transfer['id'] ?>">
                                    <i class="fas fa-check"></i>
                                </button>
                                <button class="btn btn-danger btn-sm reject-transfer" data-id="<?= $transfer['id'] ?>">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                
                <?php if (count($pendingTransfers) > 5): ?>
                <div class="text-center mt-3">
                    <a href="index.php?route=transfers&action=pending" class="btn btn-sm btn-outline-primary">
                        عرض الكل (<?= count($pendingTransfers) ?>)
                    </a>
                </div>
                <?php endif; ?>
                
                <?php else: ?>
                <div class="text-center py-4">
                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                    <p class="text-muted">لا توجد تحويلات معلقة</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Top Currencies -->
<?php if (!empty($topCurrencies)): ?>
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-coins me-2"></i>
                    أهم العملات المستخدمة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <?php foreach ($topCurrencies as $currency): ?>
                    <div class="col-md-2 col-sm-4 col-6 mb-3">
                        <div class="text-center p-3 border rounded">
                            <h4 class="text-primary"><?= $currency['code'] ?></h4>
                            <p class="mb-1"><?= $currency['name'] ?></p>
                            <small class="text-muted"><?= $currency['transfer_count'] ?> تحويل</small>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<script>
// Monthly Performance Chart
const ctx = document.getElementById('monthlyChart').getContext('2d');
const monthlyChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: <?= json_encode($monthlyStats['months']) ?>,
        datasets: [{
            label: 'عدد التحويلات',
            data: <?= json_encode($monthlyStats['transfer_counts']) ?>,
            borderColor: '#667eea',
            backgroundColor: 'rgba(102, 126, 234, 0.1)',
            tension: 0.4,
            fill: true
        }, {
            label: 'المبلغ الإجمالي',
            data: <?= json_encode($monthlyStats['amounts']) ?>,
            borderColor: '#764ba2',
            backgroundColor: 'rgba(118, 75, 162, 0.1)',
            tension: 0.4,
            fill: true,
            yAxisID: 'y1'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                type: 'linear',
                display: true,
                position: 'right',
                title: {
                    display: true,
                    text: 'عدد التحويلات'
                }
            },
            y1: {
                type: 'linear',
                display: true,
                position: 'left',
                title: {
                    display: true,
                    text: 'المبلغ'
                },
                grid: {
                    drawOnChartArea: false,
                }
            }
        },
        plugins: {
            legend: {
                position: 'top'
            }
        }
    }
});

// Approve/Reject Transfer Actions
$('.approve-transfer').on('click', function() {
    const transferId = $(this).data('id');
    
    if (confirm('هل أنت متأكد من الموافقة على هذا التحويل؟')) {
        $.post(`index.php?route=transfers&action=approve&id=${transferId}`)
            .done(function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert(response.message || 'حدث خطأ');
                }
            })
            .fail(function() {
                alert('حدث خطأ في الاتصال');
            });
    }
});

$('.reject-transfer').on('click', function() {
    const transferId = $(this).data('id');
    const reason = prompt('سبب الرفض (اختياري):');
    
    if (reason !== null) {
        $.post(`index.php?route=transfers&action=reject&id=${transferId}`, { reason: reason })
            .done(function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert(response.message || 'حدث خطأ');
                }
            })
            .fail(function() {
                alert('حدث خطأ في الاتصال');
            });
    }
});
</script>

<?php
$content = ob_get_clean();
include APP_PATH . '/Views/layouts/app.php';
?>
